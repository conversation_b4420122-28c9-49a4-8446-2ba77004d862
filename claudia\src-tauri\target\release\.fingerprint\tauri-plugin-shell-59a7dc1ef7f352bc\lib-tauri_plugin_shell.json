{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 2040997289075261528, "path": 15070104006516356483, "deps": [[500211409582349667, "shared_child", false, 12359965417467903766], [1582828171158827377, "build_script_build", false, 5160990042328742064], [5986029879202738730, "log", false, 16506617143423921061], [9451456094439810778, "regex", false, 7725510663390383755], [9538054652646069845, "tokio", false, 9712805951141166731], [9689903380558560274, "serde", false, 7541355907771353291], [10755362358622467486, "tauri", false, 263917925848210510], [10806645703491011684, "thiserror", false, 5059168433112344807], [11337703028400419576, "os_pipe", false, 12123597729035667725], [14564311161534545801, "encoding_rs", false, 12684925332375899401], [15367738274754116744, "serde_json", false, 17470562606684216992], [16192041687293812804, "open", false, 1140527685707483446]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-plugin-shell-59a7dc1ef7f352bc\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}