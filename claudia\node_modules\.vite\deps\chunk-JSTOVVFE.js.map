{"version": 3, "sources": ["../../refractor/lang/smarty.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = smarty\nsmarty.displayName = 'smarty'\nsmarty.aliases = []\nfunction smarty(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  ;(function (Prism) {\n    Prism.languages.smarty = {\n      comment: {\n        pattern: /^\\{\\*[\\s\\S]*?\\*\\}/,\n        greedy: true\n      },\n      'embedded-php': {\n        pattern: /^\\{php\\}[\\s\\S]*?\\{\\/php\\}/,\n        greedy: true,\n        inside: {\n          smarty: {\n            pattern: /^\\{php\\}|\\{\\/php\\}$/,\n            inside: null // see below\n          },\n          php: {\n            pattern: /[\\s\\S]+/,\n            alias: 'language-php',\n            inside: Prism.languages.php\n          }\n        }\n      },\n      string: [\n        {\n          pattern: /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n          greedy: true,\n          inside: {\n            interpolation: {\n              pattern: /\\{[^{}]*\\}|`[^`]*`/,\n              inside: {\n                'interpolation-punctuation': {\n                  pattern: /^[{`]|[`}]$/,\n                  alias: 'punctuation'\n                },\n                expression: {\n                  pattern: /[\\s\\S]+/,\n                  inside: null // see below\n                }\n              }\n            },\n            variable: /\\$\\w+/\n          }\n        },\n        {\n          pattern: /'(?:\\\\.|[^'\\\\\\r\\n])*'/,\n          greedy: true\n        }\n      ],\n      keyword: {\n        pattern: /(^\\{\\/?)[a-z_]\\w*\\b(?!\\()/i,\n        lookbehind: true,\n        greedy: true\n      },\n      delimiter: {\n        pattern: /^\\{\\/?|\\}$/,\n        greedy: true,\n        alias: 'punctuation'\n      },\n      number: /\\b0x[\\dA-Fa-f]+|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee][-+]?\\d+)?/,\n      variable: [\n        /\\$(?!\\d)\\w+/,\n        /#(?!\\d)\\w+#/,\n        {\n          pattern: /(\\.|->|\\w\\s*=)(?!\\d)\\w+\\b(?!\\()/,\n          lookbehind: true\n        },\n        {\n          pattern: /(\\[)(?!\\d)\\w+(?=\\])/,\n          lookbehind: true\n        }\n      ],\n      function: {\n        pattern: /(\\|\\s*)@?[a-z_]\\w*|\\b[a-z_]\\w*(?=\\()/i,\n        lookbehind: true\n      },\n      'attr-name': /\\b[a-z_]\\w*(?=\\s*=)/i,\n      boolean: /\\b(?:false|no|off|on|true|yes)\\b/,\n      punctuation: /[\\[\\](){}.,:`]|->/,\n      operator: [\n        /[+\\-*\\/%]|==?=?|[!<>]=?|&&|\\|\\|?/,\n        /\\bis\\s+(?:not\\s+)?(?:div|even|odd)(?:\\s+by)?\\b/,\n        /\\b(?:and|eq|gt?e|gt|lt?e|lt|mod|neq?|not|or)\\b/\n      ]\n    }\n    Prism.languages.smarty['embedded-php'].inside.smarty.inside =\n      Prism.languages.smarty\n    Prism.languages.smarty.string[0].inside.interpolation.inside.expression.inside =\n      Prism.languages.smarty\n    var string = /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"|'(?:\\\\.|[^'\\\\\\r\\n])*'/\n    var smartyPattern = RegExp(\n      // comments\n      /\\{\\*[\\s\\S]*?\\*\\}/.source +\n        '|' + // php tags\n        /\\{php\\}[\\s\\S]*?\\{\\/php\\}/.source +\n        '|' + // smarty blocks\n        /\\{(?:[^{}\"']|<str>|\\{(?:[^{}\"']|<str>|\\{(?:[^{}\"']|<str>)*\\})*\\})*\\}/.source.replace(\n          /<str>/g,\n          function () {\n            return string.source\n          }\n        ),\n      'g'\n    ) // Tokenize all inline Smarty expressions\n    Prism.hooks.add('before-tokenize', function (env) {\n      var smartyLiteralStart = '{literal}'\n      var smartyLiteralEnd = '{/literal}'\n      var smartyLiteralMode = false\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'smarty',\n        smartyPattern,\n        function (match) {\n          // Smarty tags inside {literal} block are ignored\n          if (match === smartyLiteralEnd) {\n            smartyLiteralMode = false\n          }\n          if (!smartyLiteralMode) {\n            if (match === smartyLiteralStart) {\n              smartyLiteralMode = true\n            }\n            return true\n          }\n          return false\n        }\n      )\n    }) // Re-insert the tokens after tokenizing\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'smarty')\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,4BAA4B;AAChC,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB,YAAM,SAAS,yBAAyB;AACvC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,SAAS;AAAA,UACvB,SAAS;AAAA,YACP,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,gBAAgB;AAAA,YACd,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,QAAQ;AAAA,gBACN,SAAS;AAAA,gBACT,QAAQ;AAAA;AAAA,cACV;AAAA,cACA,KAAK;AAAA,gBACH,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQA,OAAM,UAAU;AAAA,cAC1B;AAAA,YACF;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,eAAe;AAAA,kBACb,SAAS;AAAA,kBACT,QAAQ;AAAA,oBACN,6BAA6B;AAAA,sBAC3B,SAAS;AAAA,sBACT,OAAO;AAAA,oBACT;AAAA,oBACA,YAAY;AAAA,sBACV,SAAS;AAAA,sBACT,QAAQ;AAAA;AAAA,oBACV;AAAA,kBACF;AAAA,gBACF;AAAA,gBACA,UAAU;AAAA,cACZ;AAAA,YACF;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,UACA,SAAS;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,UACA,WAAW;AAAA,YACT,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,UACA,QAAQ;AAAA,UACR,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,UACF;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,aAAa;AAAA,UACb,SAAS;AAAA,UACT,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,OAAO,cAAc,EAAE,OAAO,OAAO,SACnDA,OAAM,UAAU;AAClB,QAAAA,OAAM,UAAU,OAAO,OAAO,CAAC,EAAE,OAAO,cAAc,OAAO,WAAW,SACtEA,OAAM,UAAU;AAClB,YAAI,SAAS;AACb,YAAI,gBAAgB;AAAA;AAAA,UAElB,mBAAmB,SACjB;AAAA,UACA,2BAA2B,SAC3B;AAAA,UACA,uEAAuE,OAAO;AAAA,YAC5E;AAAA,YACA,WAAY;AACV,qBAAO,OAAO;AAAA,YAChB;AAAA,UACF;AAAA,UACF;AAAA,QACF;AACA,QAAAA,OAAM,MAAM,IAAI,mBAAmB,SAAU,KAAK;AAChD,cAAI,qBAAqB;AACzB,cAAI,mBAAmB;AACvB,cAAI,oBAAoB;AACxB,UAAAA,OAAM,UAAU,mBAAmB,EAAE;AAAA,YACnC;AAAA,YACA;AAAA,YACA;AAAA,YACA,SAAU,OAAO;AAEf,kBAAI,UAAU,kBAAkB;AAC9B,oCAAoB;AAAA,cACtB;AACA,kBAAI,CAAC,mBAAmB;AACtB,oBAAI,UAAU,oBAAoB;AAChC,sCAAoB;AAAA,gBACtB;AACA,uBAAO;AAAA,cACT;AACA,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,UAAAA,OAAM,UAAU,mBAAmB,EAAE,qBAAqB,KAAK,QAAQ;AAAA,QACzE,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}