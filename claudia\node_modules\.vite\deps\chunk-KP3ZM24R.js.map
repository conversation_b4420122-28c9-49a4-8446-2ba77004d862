{"version": 3, "sources": ["../../highlight.js/lib/languages/yaml.js"], "sourcesContent": ["/*\nLanguage: YAML\nDescription: Yet Another Markdown Language\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nRequires: ruby.js\nWebsite: https://yaml.org\nCategory: common, config\n*/\nfunction yaml(hljs) {\n  var LITERALS = 'true false yes no null';\n\n  // YAML spec allows non-reserved URI characters in tags.\n  var URI_CHARACTERS = '[\\\\w#;/?:@&=+$,.~*\\'()[\\\\]]+';\n\n  // Define keys as starting with a word character\n  // ...containing word chars, spaces, colons, forward-slashes, hyphens and periods\n  // ...and ending with a colon followed immediately by a space, tab or newline.\n  // The YAML spec allows for much more than this, but this covers most use-cases.\n  var KEY = {\n    className: 'attr',\n    variants: [\n      { begin: '\\\\w[\\\\w :\\\\/.-]*:(?=[ \\t]|$)' },\n      { begin: '\"\\\\w[\\\\w :\\\\/.-]*\":(?=[ \\t]|$)' }, // double quoted keys\n      { begin: '\\'\\\\w[\\\\w :\\\\/.-]*\\':(?=[ \\t]|$)' } // single quoted keys\n    ]\n  };\n\n  var TEMPLATE_VARIABLES = {\n    className: 'template-variable',\n    variants: [\n      { begin: /\\{\\{/, end: /\\}\\}/ }, // jinja templates Ansible\n      { begin: /%\\{/, end: /\\}/ } // Ruby i18n\n    ]\n  };\n  var STRING = {\n    className: 'string',\n    relevance: 0,\n    variants: [\n      { begin: /'/, end: /'/ },\n      { begin: /\"/, end: /\"/ },\n      { begin: /\\S+/ }\n    ],\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      TEMPLATE_VARIABLES\n    ]\n  };\n\n  // Strings inside of value containers (objects) can't contain braces,\n  // brackets, or commas\n  var CONTAINER_STRING = hljs.inherit(STRING, {\n    variants: [\n      { begin: /'/, end: /'/ },\n      { begin: /\"/, end: /\"/ },\n      { begin: /[^\\s,{}[\\]]+/ }\n    ]\n  });\n\n  var DATE_RE = '[0-9]{4}(-[0-9][0-9]){0,2}';\n  var TIME_RE = '([Tt \\\\t][0-9][0-9]?(:[0-9][0-9]){2})?';\n  var FRACTION_RE = '(\\\\.[0-9]*)?';\n  var ZONE_RE = '([ \\\\t])*(Z|[-+][0-9][0-9]?(:[0-9][0-9])?)?';\n  var TIMESTAMP = {\n    className: 'number',\n    begin: '\\\\b' + DATE_RE + TIME_RE + FRACTION_RE + ZONE_RE + '\\\\b'\n  };\n\n  var VALUE_CONTAINER = {\n    end: ',',\n    endsWithParent: true,\n    excludeEnd: true,\n    keywords: LITERALS,\n    relevance: 0\n  };\n  var OBJECT = {\n    begin: /\\{/,\n    end: /\\}/,\n    contains: [VALUE_CONTAINER],\n    illegal: '\\\\n',\n    relevance: 0\n  };\n  var ARRAY = {\n    begin: '\\\\[',\n    end: '\\\\]',\n    contains: [VALUE_CONTAINER],\n    illegal: '\\\\n',\n    relevance: 0\n  };\n\n  var MODES = [\n    KEY,\n    {\n      className: 'meta',\n      begin: '^---\\\\s*$',\n      relevance: 10\n    },\n    { // multi line string\n      // Blocks start with a | or > followed by a newline\n      //\n      // Indentation of subsequent lines must be the same to\n      // be considered part of the block\n      className: 'string',\n      begin: '[\\\\|>]([1-9]?[+-])?[ ]*\\\\n( +)[^ ][^\\\\n]*\\\\n(\\\\2[^\\\\n]+\\\\n?)*'\n    },\n    { // Ruby/Rails erb\n      begin: '<%[%=-]?',\n      end: '[%-]?%>',\n      subLanguage: 'ruby',\n      excludeBegin: true,\n      excludeEnd: true,\n      relevance: 0\n    },\n    { // named tags\n      className: 'type',\n      begin: '!\\\\w+!' + URI_CHARACTERS\n    },\n    // https://yaml.org/spec/1.2/spec.html#id2784064\n    { // verbatim tags\n      className: 'type',\n      begin: '!<' + URI_CHARACTERS + \">\"\n    },\n    { // primary tags\n      className: 'type',\n      begin: '!' + URI_CHARACTERS\n    },\n    { // secondary tags\n      className: 'type',\n      begin: '!!' + URI_CHARACTERS\n    },\n    { // fragment id &ref\n      className: 'meta',\n      begin: '&' + hljs.UNDERSCORE_IDENT_RE + '$'\n    },\n    { // fragment reference *ref\n      className: 'meta',\n      begin: '\\\\*' + hljs.UNDERSCORE_IDENT_RE + '$'\n    },\n    { // array listing\n      className: 'bullet',\n      // TODO: remove |$ hack when we have proper look-ahead support\n      begin: '-(?=[ ]|$)',\n      relevance: 0\n    },\n    hljs.HASH_COMMENT_MODE,\n    {\n      beginKeywords: LITERALS,\n      keywords: { literal: LITERALS }\n    },\n    TIMESTAMP,\n    // numbers are any valid C-style number that\n    // sit isolated from other words\n    {\n      className: 'number',\n      begin: hljs.C_NUMBER_RE + '\\\\b',\n      relevance: 0\n    },\n    OBJECT,\n    ARRAY,\n    STRING\n  ];\n\n  var VALUE_MODES = [...MODES];\n  VALUE_MODES.pop();\n  VALUE_MODES.push(CONTAINER_STRING);\n  VALUE_CONTAINER.contains = VALUE_MODES;\n\n  return {\n    name: 'YAML',\n    case_insensitive: true,\n    aliases: [ 'yml' ],\n    contains: MODES\n  };\n}\n\nmodule.exports = yaml;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,KAAK,MAAM;AAClB,UAAI,WAAW;AAGf,UAAI,iBAAiB;AAMrB,UAAI,MAAM;AAAA,QACR,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAE,OAAO,8BAA+B;AAAA,UACxC,EAAE,OAAO,gCAAiC;AAAA;AAAA,UAC1C,EAAE,OAAO,gCAAmC;AAAA;AAAA,QAC9C;AAAA,MACF;AAEA,UAAI,qBAAqB;AAAA,QACvB,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAE,OAAO,QAAQ,KAAK,OAAO;AAAA;AAAA,UAC7B,EAAE,OAAO,OAAO,KAAK,KAAK;AAAA;AAAA,QAC5B;AAAA,MACF;AACA,UAAI,SAAS;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAE,OAAO,KAAK,KAAK,IAAI;AAAA,UACvB,EAAE,OAAO,KAAK,KAAK,IAAI;AAAA,UACvB,EAAE,OAAO,MAAM;AAAA,QACjB;AAAA,QACA,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAIA,UAAI,mBAAmB,KAAK,QAAQ,QAAQ;AAAA,QAC1C,UAAU;AAAA,UACR,EAAE,OAAO,KAAK,KAAK,IAAI;AAAA,UACvB,EAAE,OAAO,KAAK,KAAK,IAAI;AAAA,UACvB,EAAE,OAAO,eAAe;AAAA,QAC1B;AAAA,MACF,CAAC;AAED,UAAI,UAAU;AACd,UAAI,UAAU;AACd,UAAI,cAAc;AAClB,UAAI,UAAU;AACd,UAAI,YAAY;AAAA,QACd,WAAW;AAAA,QACX,OAAO,QAAQ,UAAU,UAAU,cAAc,UAAU;AAAA,MAC7D;AAEA,UAAI,kBAAkB;AAAA,QACpB,KAAK;AAAA,QACL,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,WAAW;AAAA,MACb;AACA,UAAI,SAAS;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAC,eAAe;AAAA,QAC1B,SAAS;AAAA,QACT,WAAW;AAAA,MACb;AACA,UAAI,QAAQ;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAC,eAAe;AAAA,QAC1B,SAAS;AAAA,QACT,WAAW;AAAA,MACb;AAEA,UAAI,QAAQ;AAAA,QACV;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,UACP,WAAW;AAAA,QACb;AAAA,QACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAKE,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA,QACA;AAAA;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,UACL,aAAa;AAAA,UACb,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,WAAW;AAAA,QACb;AAAA,QACA;AAAA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,WAAW;AAAA,QACpB;AAAA;AAAA,QAEA;AAAA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,OAAO,iBAAiB;AAAA,QACjC;AAAA,QACA;AAAA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,MAAM;AAAA,QACf;AAAA,QACA;AAAA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,OAAO;AAAA,QAChB;AAAA,QACA;AAAA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,MAAM,KAAK,sBAAsB;AAAA,QAC1C;AAAA,QACA;AAAA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,QAAQ,KAAK,sBAAsB;AAAA,QAC5C;AAAA,QACA;AAAA;AAAA,UACE,WAAW;AAAA;AAAA,UAEX,OAAO;AAAA,UACP,WAAW;AAAA,QACb;AAAA,QACA,KAAK;AAAA,QACL;AAAA,UACE,eAAe;AAAA,UACf,UAAU,EAAE,SAAS,SAAS;AAAA,QAChC;AAAA,QACA;AAAA;AAAA;AAAA,QAGA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,KAAK,cAAc;AAAA,UAC1B,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,UAAI,cAAc,CAAC,GAAG,KAAK;AAC3B,kBAAY,IAAI;AAChB,kBAAY,KAAK,gBAAgB;AACjC,sBAAgB,WAAW;AAE3B,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,SAAS,CAAE,KAAM;AAAA,QACjB,UAAU;AAAA,MACZ;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}