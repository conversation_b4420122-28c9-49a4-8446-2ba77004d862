{"version": 3, "sources": ["../../highlight.js/lib/languages/flix.js"], "sourcesContent": ["/*\n Language: Flix\n Category: functional\n Author: <PERSON> <mm<PERSON><EMAIL>>\n Website: https://flix.dev/\n */\n\n /** @type LanguageFn */\nfunction flix(hljs) {\n  const CHAR = {\n    className: 'string',\n    begin: /'(.|\\\\[xXuU][a-zA-Z0-9]+)'/\n  };\n\n  const STRING = {\n    className: 'string',\n    variants: [{\n      begin: '\"',\n      end: '\"'\n    }]\n  };\n\n  const NAME = {\n    className: 'title',\n    relevance: 0,\n    begin: /[^0-9\\n\\t \"'(),.`{}\\[\\]:;][^\\n\\t \"'(),.`{}\\[\\]:;]+|[^0-9\\n\\t \"'(),.`{}\\[\\]:;=]/\n  };\n\n  const METHOD = {\n    className: 'function',\n    beginKeywords: 'def',\n    end: /[:={\\[(\\n;]/,\n    excludeEnd: true,\n    contains: [NAME]\n  };\n\n  return {\n    name: 'Flix',\n    keywords: {\n      literal: 'true false',\n      keyword: 'case class def else enum if impl import in lat rel index let match namespace switch type yield with'\n    },\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      CHAR,\n      STRING,\n      METHOD,\n      hljs.C_NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = flix;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,KAAK,MAAM;AAClB,YAAM,OAAO;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACT,OAAO;AAAA,UACP,KAAK;AAAA,QACP,CAAC;AAAA,MACH;AAEA,YAAM,OAAO;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,eAAe;AAAA,QACf,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,UAAU,CAAC,IAAI;AAAA,MACjB;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,UACR,SAAS;AAAA,UACT,SAAS;AAAA,QACX;AAAA,QACA,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}