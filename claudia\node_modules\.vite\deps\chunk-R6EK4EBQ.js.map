{"version": 3, "sources": ["../../highlight.js/lib/languages/purebasic.js"], "sourcesContent": ["/*\nLanguage: PureBASIC\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: Syntax highlighting for PureBASIC (v.5.00-5.60). No inline ASM highlighting. (v.1.2, May 2017)\nCredits: I've taken inspiration from the PureBasic language file for GeSHi, created by <PERSON> (GuShH).\nWebsite: https://www.purebasic.com\n*/\n\n// Base deafult colors in PB IDE: background: #FFFFDF; foreground: #000000;\n\nfunction purebasic(hljs) {\n  const STRINGS = { // PB IDE color: #0080FF (Azure Radiance)\n    className: 'string',\n    begin: '(~)?\"',\n    end: '\"',\n    illegal: '\\\\n'\n  };\n  const CONSTANTS = { // PB IDE color: #924B72 (Cannon Pink)\n    //  \"#\" + a letter or underscore + letters, digits or underscores + (optional) \"$\"\n    className: 'symbol',\n    begin: '#[a-zA-Z_]\\\\w*\\\\$?'\n  };\n\n  return {\n    name: 'PureBASIC',\n    aliases: [\n      'pb',\n      'pbi'\n    ],\n    keywords: // PB IDE color: #006666 (<PERSON>) + Bold\n      // Keywords from all version of PureBASIC 5.00 upward ...\n      'Align And Array As Break CallDebugger Case CompilerCase CompilerDefault ' +\n      'CompilerElse CompilerElseIf CompilerEndIf CompilerEndSelect CompilerError ' +\n      'CompilerIf CompilerSelect CompilerWarning Continue Data DataSection Debug ' +\n      'DebugLevel Declare DeclareC DeclareCDLL DeclareDLL DeclareModule Default ' +\n      'Define Dim DisableASM DisableDebugger DisableExplicit Else ElseIf EnableASM ' +\n      'EnableDebugger EnableExplicit End EndDataSection EndDeclareModule EndEnumeration ' +\n      'EndIf EndImport EndInterface EndMacro EndModule EndProcedure EndSelect ' +\n      'EndStructure EndStructureUnion EndWith Enumeration EnumerationBinary Extends ' +\n      'FakeReturn For ForEach ForEver Global Gosub Goto If Import ImportC ' +\n      'IncludeBinary IncludeFile IncludePath Interface List Macro MacroExpandedCount ' +\n      'Map Module NewList NewMap Next Not Or Procedure ProcedureC ' +\n      'ProcedureCDLL ProcedureDLL ProcedureReturn Protected Prototype PrototypeC ReDim ' +\n      'Read Repeat Restore Return Runtime Select Shared Static Step Structure ' +\n      'StructureUnion Swap Threaded To UndefineMacro Until Until  UnuseModule ' +\n      'UseModule Wend While With XIncludeFile XOr',\n    contains: [\n      // COMMENTS | PB IDE color: #00AAAA (Persian Green)\n      hljs.COMMENT(';', '$', {\n        relevance: 0\n      }),\n\n      { // PROCEDURES DEFINITIONS\n        className: 'function',\n        begin: '\\\\b(Procedure|Declare)(C|CDLL|DLL)?\\\\b',\n        end: '\\\\(',\n        excludeEnd: true,\n        returnBegin: true,\n        contains: [\n          { // PROCEDURE KEYWORDS | PB IDE color: #006666 (Blue Stone) + Bold\n            className: 'keyword',\n            begin: '(Procedure|Declare)(C|CDLL|DLL)?',\n            excludeEnd: true\n          },\n          { // PROCEDURE RETURN TYPE SETTING | PB IDE color: #000000 (Black)\n            className: 'type',\n            begin: '\\\\.\\\\w*'\n            // end: ' ',\n          },\n          hljs.UNDERSCORE_TITLE_MODE // PROCEDURE NAME | PB IDE color: #006666 (Blue Stone)\n        ]\n      },\n      STRINGS,\n      CONSTANTS\n    ]\n  };\n}\n\n/*  ==============================================================================\n                                      CHANGELOG\n    ==============================================================================\n    - v.1.2 (2017-05-12)\n        -- BUG-FIX: Some keywords were accidentally joyned together. Now fixed.\n    - v.1.1 (2017-04-30)\n        -- Updated to PureBASIC 5.60.\n        -- Keywords list now built by extracting them from the PureBASIC SDK's\n           \"SyntaxHilighting.dll\" (from each PureBASIC version). Tokens from each\n           version are added to the list, and renamed or removed tokens are kept\n           for the sake of covering all versions of the language from PureBASIC\n           v5.00 upward. (NOTE: currently, there are no renamed or deprecated\n           tokens in the keywords list). For more info, see:\n           -- http://www.purebasic.fr/english/viewtopic.php?&p=506269\n           -- https://github.com/tajmone/purebasic-archives/tree/master/syntax-highlighting/guidelines\n    - v.1.0 (April 2016)\n        -- First release\n        -- Keywords list taken and adapted from GuShH's (Gustavo Julio Fiorenza)\n           PureBasic language file for GeSHi:\n           -- https://github.com/easybook/geshi/blob/master/geshi/purebasic.php\n*/\n\nmodule.exports = purebasic;\n"], "mappings": ";;;;;AAAA;AAAA;AAUA,aAAS,UAAU,MAAM;AACvB,YAAM,UAAU;AAAA;AAAA,QACd,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,MACX;AACA,YAAM,YAAY;AAAA;AAAA;AAAA,QAEhB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,QACA;AAAA;AAAA;AAAA,UAEE;AAAA;AAAA,QAeF,UAAU;AAAA;AAAA,UAER,KAAK,QAAQ,KAAK,KAAK;AAAA,YACrB,WAAW;AAAA,UACb,CAAC;AAAA,UAED;AAAA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,aAAa;AAAA,YACb,UAAU;AAAA,cACR;AAAA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,YAAY;AAAA,cACd;AAAA,cACA;AAAA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA;AAAA,cAET;AAAA,cACA,KAAK;AAAA;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAwBA,WAAO,UAAU;AAAA;AAAA;", "names": []}