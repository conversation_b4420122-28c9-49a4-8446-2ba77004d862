{"version": 3, "sources": ["../../refractor/lang/t4-templating.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = t4Templating\nt4Templating.displayName = 't4Templating'\nt4Templating.aliases = []\nfunction t4Templating(Prism) {\n  ;(function (Prism) {\n    function createBlock(prefix, inside, contentAlias) {\n      return {\n        pattern: RegExp('<#' + prefix + '[\\\\s\\\\S]*?#>'),\n        alias: 'block',\n        inside: {\n          delimiter: {\n            pattern: RegExp('^<#' + prefix + '|#>$'),\n            alias: 'important'\n          },\n          content: {\n            pattern: /[\\s\\S]+/,\n            inside: inside,\n            alias: contentAlia<PERSON>\n          }\n        }\n      }\n    }\n    function createT4(insideLang) {\n      var grammar = Prism.languages[insideLang]\n      var className = 'language-' + insideLang\n      return {\n        block: {\n          pattern: /<#[\\s\\S]+?#>/,\n          inside: {\n            directive: createBlock('@', {\n              'attr-value': {\n                pattern: /=(?:(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|[^\\s'\">=]+)/,\n                inside: {\n                  punctuation: /^=|^[\"']|[\"']$/\n                }\n              },\n              keyword: /\\b\\w+(?=\\s)/,\n              'attr-name': /\\b\\w+/\n            }),\n            expression: createBlock('=', grammar, className),\n            'class-feature': createBlock('\\\\+', grammar, className),\n            standard: createBlock('', grammar, className)\n          }\n        }\n      }\n    }\n    Prism.languages['t4-templating'] = Object.defineProperty({}, 'createT4', {\n      value: createT4\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,iBAAa,cAAc;AAC3B,iBAAa,UAAU,CAAC;AACxB,aAAS,aAAa,OAAO;AAC3B;AAAC,OAAC,SAAUA,QAAO;AACjB,iBAAS,YAAY,QAAQ,QAAQ,cAAc;AACjD,iBAAO;AAAA,YACL,SAAS,OAAO,OAAO,SAAS,cAAc;AAAA,YAC9C,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,WAAW;AAAA,gBACT,SAAS,OAAO,QAAQ,SAAS,MAAM;AAAA,gBACvC,OAAO;AAAA,cACT;AAAA,cACA,SAAS;AAAA,gBACP,SAAS;AAAA,gBACT;AAAA,gBACA,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,iBAAS,SAAS,YAAY;AAC5B,cAAI,UAAUA,OAAM,UAAU,UAAU;AACxC,cAAI,YAAY,cAAc;AAC9B,iBAAO;AAAA,YACL,OAAO;AAAA,cACL,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,WAAW,YAAY,KAAK;AAAA,kBAC1B,cAAc;AAAA,oBACZ,SAAS;AAAA,oBACT,QAAQ;AAAA,sBACN,aAAa;AAAA,oBACf;AAAA,kBACF;AAAA,kBACA,SAAS;AAAA,kBACT,aAAa;AAAA,gBACf,CAAC;AAAA,gBACD,YAAY,YAAY,KAAK,SAAS,SAAS;AAAA,gBAC/C,iBAAiB,YAAY,OAAO,SAAS,SAAS;AAAA,gBACtD,UAAU,YAAY,IAAI,SAAS,SAAS;AAAA,cAC9C;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,eAAe,IAAI,OAAO,eAAe,CAAC,GAAG,YAAY;AAAA,UACvE,OAAO;AAAA,QACT,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}