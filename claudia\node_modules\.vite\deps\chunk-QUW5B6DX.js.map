{"version": 3, "sources": ["../../refractor/lang/robotframework.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = robotframework\nrobotframework.displayName = 'robotframework'\nrobotframework.aliases = []\nfunction robotframework(Prism) {\n  ;(function (Prism) {\n    var comment = {\n      pattern: /(^[ \\t]*| {2}|\\t)#.*/m,\n      lookbehind: true,\n      greedy: true\n    }\n    var variable = {\n      pattern: /((?:^|[^\\\\])(?:\\\\{2})*)[$@&%]\\{(?:[^{}\\r\\n]|\\{[^{}\\r\\n]*\\})*\\}/,\n      lookbehind: true,\n      inside: {\n        punctuation: /^[$@&%]\\{|\\}$/\n      }\n    }\n    function createSection(name, inside) {\n      var extendecInside = {}\n      extendecInside['section-header'] = {\n        pattern: /^ ?\\*{3}.+?\\*{3}/,\n        alias: 'keyword'\n      } // copy inside tokens\n      for (var token in inside) {\n        extendecInside[token] = inside[token]\n      }\n      extendecInside['tag'] = {\n        pattern: /([\\r\\n](?: {2}|\\t)[ \\t]*)\\[[-\\w]+\\]/,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\[|\\]/\n        }\n      }\n      extendecInside['variable'] = variable\n      extendecInside['comment'] = comment\n      return {\n        pattern: RegExp(\n          /^ ?\\*{3}[ \\t]*<name>[ \\t]*\\*{3}(?:.|[\\r\\n](?!\\*{3}))*/.source.replace(\n            /<name>/g,\n            function () {\n              return name\n            }\n          ),\n          'im'\n        ),\n        alias: 'section',\n        inside: extendecInside\n      }\n    }\n    var docTag = {\n      pattern:\n        /(\\[Documentation\\](?: {2}|\\t)[ \\t]*)(?![ \\t]|#)(?:.|(?:\\r\\n?|\\n)[ \\t]*\\.{3})+/,\n      lookbehind: true,\n      alias: 'string'\n    }\n    var testNameLike = {\n      pattern: /([\\r\\n] ?)(?!#)(?:\\S(?:[ \\t]\\S)*)+/,\n      lookbehind: true,\n      alias: 'function',\n      inside: {\n        variable: variable\n      }\n    }\n    var testPropertyLike = {\n      pattern: /([\\r\\n](?: {2}|\\t)[ \\t]*)(?!\\[|\\.{3}|#)(?:\\S(?:[ \\t]\\S)*)+/,\n      lookbehind: true,\n      inside: {\n        variable: variable\n      }\n    }\n    Prism.languages['robotframework'] = {\n      settings: createSection('Settings', {\n        documentation: {\n          pattern:\n            /([\\r\\n] ?Documentation(?: {2}|\\t)[ \\t]*)(?![ \\t]|#)(?:.|(?:\\r\\n?|\\n)[ \\t]*\\.{3})+/,\n          lookbehind: true,\n          alias: 'string'\n        },\n        property: {\n          pattern: /([\\r\\n] ?)(?!\\.{3}|#)(?:\\S(?:[ \\t]\\S)*)+/,\n          lookbehind: true\n        }\n      }),\n      variables: createSection('Variables'),\n      'test-cases': createSection('Test Cases', {\n        'test-name': testNameLike,\n        documentation: docTag,\n        property: testPropertyLike\n      }),\n      keywords: createSection('Keywords', {\n        'keyword-name': testNameLike,\n        documentation: docTag,\n        property: testPropertyLike\n      }),\n      tasks: createSection('Tasks', {\n        'task-name': testNameLike,\n        documentation: docTag,\n        property: testPropertyLike\n      }),\n      comment: comment\n    }\n    Prism.languages.robot = Prism.languages['robotframework']\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,mBAAe,cAAc;AAC7B,mBAAe,UAAU,CAAC;AAC1B,aAAS,eAAe,OAAO;AAC7B;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,UAAU;AAAA,UACZ,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AACA,YAAI,WAAW;AAAA,UACb,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,aAAa;AAAA,UACf;AAAA,QACF;AACA,iBAAS,cAAc,MAAM,QAAQ;AACnC,cAAI,iBAAiB,CAAC;AACtB,yBAAe,gBAAgB,IAAI;AAAA,YACjC,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AACA,mBAAS,SAAS,QAAQ;AACxB,2BAAe,KAAK,IAAI,OAAO,KAAK;AAAA,UACtC;AACA,yBAAe,KAAK,IAAI;AAAA,YACtB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AACA,yBAAe,UAAU,IAAI;AAC7B,yBAAe,SAAS,IAAI;AAC5B,iBAAO;AAAA,YACL,SAAS;AAAA,cACP,wDAAwD,OAAO;AAAA,gBAC7D;AAAA,gBACA,WAAY;AACV,yBAAO;AAAA,gBACT;AAAA,cACF;AAAA,cACA;AAAA,YACF;AAAA,YACA,OAAO;AAAA,YACP,QAAQ;AAAA,UACV;AAAA,QACF;AACA,YAAI,SAAS;AAAA,UACX,SACE;AAAA,UACF,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AACA,YAAI,eAAe;AAAA,UACjB,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,QAAQ;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,YAAI,mBAAmB;AAAA,UACrB,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,gBAAgB,IAAI;AAAA,UAClC,UAAU,cAAc,YAAY;AAAA,YAClC,eAAe;AAAA,cACb,SACE;AAAA,cACF,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,YACA,UAAU;AAAA,cACR,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,UACF,CAAC;AAAA,UACD,WAAW,cAAc,WAAW;AAAA,UACpC,cAAc,cAAc,cAAc;AAAA,YACxC,aAAa;AAAA,YACb,eAAe;AAAA,YACf,UAAU;AAAA,UACZ,CAAC;AAAA,UACD,UAAU,cAAc,YAAY;AAAA,YAClC,gBAAgB;AAAA,YAChB,eAAe;AAAA,YACf,UAAU;AAAA,UACZ,CAAC;AAAA,UACD,OAAO,cAAc,SAAS;AAAA,YAC5B,aAAa;AAAA,YACb,eAAe;AAAA,YACf,UAAU;AAAA,UACZ,CAAC;AAAA,UACD;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,QAAQA,OAAM,UAAU,gBAAgB;AAAA,MAC1D,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}