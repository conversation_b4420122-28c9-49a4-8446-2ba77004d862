{"version": 3, "sources": ["../../refractor/lang/cfscript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = cfscript\ncfscript.displayName = 'cfscript'\ncfscript.aliases = []\nfunction cfscript(Prism) {\n  // https://cfdocs.org/script\n  Prism.languages.cfscript = Prism.languages.extend('clike', {\n    comment: [\n      {\n        pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n        lookbehind: true,\n        inside: {\n          annotation: {\n            pattern: /(?:^|[^.])@[\\w\\.]+/,\n            alias: 'punctuation'\n          }\n        }\n      },\n      {\n        pattern: /(^|[^\\\\:])\\/\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    keyword:\n      /\\b(?:abstract|break|catch|component|continue|default|do|else|extends|final|finally|for|function|if|in|include|package|private|property|public|remote|required|rethrow|return|static|switch|throw|try|var|while|xml)\\b(?!\\s*=)/,\n    operator: [\n      /\\+\\+|--|&&|\\|\\||::|=>|[!=]==|<=?|>=?|[-+*/%&|^!=<>]=?|\\?(?:\\.|:)?|[?:]/,\n      /\\b(?:and|contains|eq|equal|eqv|gt|gte|imp|is|lt|lte|mod|not|or|xor)\\b/\n    ],\n    scope: {\n      pattern:\n        /\\b(?:application|arguments|cgi|client|cookie|local|session|super|this|variables)\\b/,\n      alias: 'global'\n    },\n    type: {\n      pattern:\n        /\\b(?:any|array|binary|boolean|date|guid|numeric|query|string|struct|uuid|void|xml)\\b/,\n      alias: 'builtin'\n    }\n  })\n  Prism.languages.insertBefore('cfscript', 'keyword', {\n    // This must be declared before keyword because we use \"function\" inside the lookahead\n    'function-variable': {\n      pattern:\n        /[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*[=:]\\s*(?:\\bfunction\\b|(?:\\((?:[^()]|\\([^()]*\\))*\\)|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/,\n      alias: 'function'\n    }\n  })\n  delete Prism.languages.cfscript['class-name']\n  Prism.languages.cfc = Prism.languages['cfscript']\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,aAAS,cAAc;AACvB,aAAS,UAAU,CAAC;AACpB,aAAS,SAAS,OAAO;AAEvB,YAAM,UAAU,WAAW,MAAM,UAAU,OAAO,SAAS;AAAA,QACzD,SAAS;AAAA,UACP;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,YAAY;AAAA,gBACV,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,QACA,SACE;AAAA,QACF,UAAU;AAAA,UACR;AAAA,UACA;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL,SACE;AAAA,UACF,OAAO;AAAA,QACT;AAAA,QACA,MAAM;AAAA,UACJ,SACE;AAAA,UACF,OAAO;AAAA,QACT;AAAA,MACF,CAAC;AACD,YAAM,UAAU,aAAa,YAAY,WAAW;AAAA;AAAA,QAElD,qBAAqB;AAAA,UACnB,SACE;AAAA,UACF,OAAO;AAAA,QACT;AAAA,MACF,CAAC;AACD,aAAO,MAAM,UAAU,SAAS,YAAY;AAC5C,YAAM,UAAU,MAAM,MAAM,UAAU,UAAU;AAAA,IAClD;AAAA;AAAA;", "names": []}