{"version": 3, "sources": ["../../refractor/lang/jsx.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = jsx\njsx.displayName = 'jsx'\njsx.aliases = []\nfunction jsx(Prism) {\n  ;(function (Prism) {\n    var javascript = Prism.util.clone(Prism.languages.javascript)\n    var space = /(?:\\s|\\/\\/.*(?!.)|\\/\\*(?:[^*]|\\*(?!\\/))\\*\\/)/.source\n    var braces = /(?:\\{(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])*\\})/.source\n    var spread = /(?:\\{<S>*\\.{3}(?:[^{}]|<BRACES>)*\\})/.source\n    /**\n     * @param {string} source\n     * @param {string} [flags]\n     */\n    function re(source, flags) {\n      source = source\n        .replace(/<S>/g, function () {\n          return space\n        })\n        .replace(/<BRACES>/g, function () {\n          return braces\n        })\n        .replace(/<SPREAD>/g, function () {\n          return spread\n        })\n      return RegExp(source, flags)\n    }\n    spread = re(spread).source\n    Prism.languages.jsx = Prism.languages.extend('markup', javascript)\n    Prism.languages.jsx.tag.pattern = re(\n      /<\\/?(?:[\\w.:-]+(?:<S>+(?:[\\w.:$-]+(?:=(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*'|[^\\s{'\"/>=]+|<BRACES>))?|<SPREAD>))*<S>*\\/?)?>/\n        .source\n    )\n    Prism.languages.jsx.tag.inside['tag'].pattern = /^<\\/?[^\\s>\\/]*/\n    Prism.languages.jsx.tag.inside['attr-value'].pattern =\n      /=(?!\\{)(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*'|[^\\s'\">]+)/\n    Prism.languages.jsx.tag.inside['tag'].inside['class-name'] =\n      /^[A-Z]\\w*(?:\\.[A-Z]\\w*)*$/\n    Prism.languages.jsx.tag.inside['comment'] = javascript['comment']\n    Prism.languages.insertBefore(\n      'inside',\n      'attr-name',\n      {\n        spread: {\n          pattern: re(/<SPREAD>/.source),\n          inside: Prism.languages.jsx\n        }\n      },\n      Prism.languages.jsx.tag\n    )\n    Prism.languages.insertBefore(\n      'inside',\n      'special-attr',\n      {\n        script: {\n          // Allow for two levels of nesting\n          pattern: re(/=<BRACES>/.source),\n          alias: 'language-javascript',\n          inside: {\n            'script-punctuation': {\n              pattern: /^=(?=\\{)/,\n              alias: 'punctuation'\n            },\n            rest: Prism.languages.jsx\n          }\n        }\n      },\n      Prism.languages.jsx.tag\n    ) // The following will handle plain text inside tags\n    var stringifyToken = function (token) {\n      if (!token) {\n        return ''\n      }\n      if (typeof token === 'string') {\n        return token\n      }\n      if (typeof token.content === 'string') {\n        return token.content\n      }\n      return token.content.map(stringifyToken).join('')\n    }\n    var walkTokens = function (tokens) {\n      var openedTags = []\n      for (var i = 0; i < tokens.length; i++) {\n        var token = tokens[i]\n        var notTagNorBrace = false\n        if (typeof token !== 'string') {\n          if (\n            token.type === 'tag' &&\n            token.content[0] &&\n            token.content[0].type === 'tag'\n          ) {\n            // We found a tag, now find its kind\n            if (token.content[0].content[0].content === '</') {\n              // Closing tag\n              if (\n                openedTags.length > 0 &&\n                openedTags[openedTags.length - 1].tagName ===\n                  stringifyToken(token.content[0].content[1])\n              ) {\n                // Pop matching opening tag\n                openedTags.pop()\n              }\n            } else {\n              if (token.content[token.content.length - 1].content === '/>') {\n                // Autoclosed tag, ignore\n              } else {\n                // Opening tag\n                openedTags.push({\n                  tagName: stringifyToken(token.content[0].content[1]),\n                  openedBraces: 0\n                })\n              }\n            }\n          } else if (\n            openedTags.length > 0 &&\n            token.type === 'punctuation' &&\n            token.content === '{'\n          ) {\n            // Here we might have entered a JSX context inside a tag\n            openedTags[openedTags.length - 1].openedBraces++\n          } else if (\n            openedTags.length > 0 &&\n            openedTags[openedTags.length - 1].openedBraces > 0 &&\n            token.type === 'punctuation' &&\n            token.content === '}'\n          ) {\n            // Here we might have left a JSX context inside a tag\n            openedTags[openedTags.length - 1].openedBraces--\n          } else {\n            notTagNorBrace = true\n          }\n        }\n        if (notTagNorBrace || typeof token === 'string') {\n          if (\n            openedTags.length > 0 &&\n            openedTags[openedTags.length - 1].openedBraces === 0\n          ) {\n            // Here we are inside a tag, and not inside a JSX context.\n            // That's plain text: drop any tokens matched.\n            var plainText = stringifyToken(token) // And merge text with adjacent text\n            if (\n              i < tokens.length - 1 &&\n              (typeof tokens[i + 1] === 'string' ||\n                tokens[i + 1].type === 'plain-text')\n            ) {\n              plainText += stringifyToken(tokens[i + 1])\n              tokens.splice(i + 1, 1)\n            }\n            if (\n              i > 0 &&\n              (typeof tokens[i - 1] === 'string' ||\n                tokens[i - 1].type === 'plain-text')\n            ) {\n              plainText = stringifyToken(tokens[i - 1]) + plainText\n              tokens.splice(i - 1, 1)\n              i--\n            }\n            tokens[i] = new Prism.Token(\n              'plain-text',\n              plainText,\n              null,\n              plainText\n            )\n          }\n        }\n        if (token.content && typeof token.content !== 'string') {\n          walkTokens(token.content)\n        }\n      }\n    }\n    Prism.hooks.add('after-tokenize', function (env) {\n      if (env.language !== 'jsx' && env.language !== 'tsx') {\n        return\n      }\n      walkTokens(env.tokens)\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAClB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,aAAaA,OAAM,KAAK,MAAMA,OAAM,UAAU,UAAU;AAC5D,YAAI,QAAQ,+CAA+C;AAC3D,YAAI,SAAS,+CAA+C;AAC5D,YAAI,SAAS,uCAAuC;AAKpD,iBAAS,GAAG,QAAQ,OAAO;AACzB,mBAAS,OACN,QAAQ,QAAQ,WAAY;AAC3B,mBAAO;AAAA,UACT,CAAC,EACA,QAAQ,aAAa,WAAY;AAChC,mBAAO;AAAA,UACT,CAAC,EACA,QAAQ,aAAa,WAAY;AAChC,mBAAO;AAAA,UACT,CAAC;AACH,iBAAO,OAAO,QAAQ,KAAK;AAAA,QAC7B;AACA,iBAAS,GAAG,MAAM,EAAE;AACpB,QAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU,OAAO,UAAU,UAAU;AACjE,QAAAA,OAAM,UAAU,IAAI,IAAI,UAAU;AAAA,UAChC,wIACG;AAAA,QACL;AACA,QAAAA,OAAM,UAAU,IAAI,IAAI,OAAO,KAAK,EAAE,UAAU;AAChD,QAAAA,OAAM,UAAU,IAAI,IAAI,OAAO,YAAY,EAAE,UAC3C;AACF,QAAAA,OAAM,UAAU,IAAI,IAAI,OAAO,KAAK,EAAE,OAAO,YAAY,IACvD;AACF,QAAAA,OAAM,UAAU,IAAI,IAAI,OAAO,SAAS,IAAI,WAAW,SAAS;AAChE,QAAAA,OAAM,UAAU;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,YACE,QAAQ;AAAA,cACN,SAAS,GAAG,WAAW,MAAM;AAAA,cAC7B,QAAQA,OAAM,UAAU;AAAA,YAC1B;AAAA,UACF;AAAA,UACAA,OAAM,UAAU,IAAI;AAAA,QACtB;AACA,QAAAA,OAAM,UAAU;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,YACE,QAAQ;AAAA;AAAA,cAEN,SAAS,GAAG,YAAY,MAAM;AAAA,cAC9B,OAAO;AAAA,cACP,QAAQ;AAAA,gBACN,sBAAsB;AAAA,kBACpB,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA,gBACA,MAAMA,OAAM,UAAU;AAAA,cACxB;AAAA,YACF;AAAA,UACF;AAAA,UACAA,OAAM,UAAU,IAAI;AAAA,QACtB;AACA,YAAI,iBAAiB,SAAU,OAAO;AACpC,cAAI,CAAC,OAAO;AACV,mBAAO;AAAA,UACT;AACA,cAAI,OAAO,UAAU,UAAU;AAC7B,mBAAO;AAAA,UACT;AACA,cAAI,OAAO,MAAM,YAAY,UAAU;AACrC,mBAAO,MAAM;AAAA,UACf;AACA,iBAAO,MAAM,QAAQ,IAAI,cAAc,EAAE,KAAK,EAAE;AAAA,QAClD;AACA,YAAI,aAAa,SAAU,QAAQ;AACjC,cAAI,aAAa,CAAC;AAClB,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,gBAAI,QAAQ,OAAO,CAAC;AACpB,gBAAI,iBAAiB;AACrB,gBAAI,OAAO,UAAU,UAAU;AAC7B,kBACE,MAAM,SAAS,SACf,MAAM,QAAQ,CAAC,KACf,MAAM,QAAQ,CAAC,EAAE,SAAS,OAC1B;AAEA,oBAAI,MAAM,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,YAAY,MAAM;AAEhD,sBACE,WAAW,SAAS,KACpB,WAAW,WAAW,SAAS,CAAC,EAAE,YAChC,eAAe,MAAM,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC,GAC5C;AAEA,+BAAW,IAAI;AAAA,kBACjB;AAAA,gBACF,OAAO;AACL,sBAAI,MAAM,QAAQ,MAAM,QAAQ,SAAS,CAAC,EAAE,YAAY,MAAM;AAAA,kBAE9D,OAAO;AAEL,+BAAW,KAAK;AAAA,sBACd,SAAS,eAAe,MAAM,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC;AAAA,sBACnD,cAAc;AAAA,oBAChB,CAAC;AAAA,kBACH;AAAA,gBACF;AAAA,cACF,WACE,WAAW,SAAS,KACpB,MAAM,SAAS,iBACf,MAAM,YAAY,KAClB;AAEA,2BAAW,WAAW,SAAS,CAAC,EAAE;AAAA,cACpC,WACE,WAAW,SAAS,KACpB,WAAW,WAAW,SAAS,CAAC,EAAE,eAAe,KACjD,MAAM,SAAS,iBACf,MAAM,YAAY,KAClB;AAEA,2BAAW,WAAW,SAAS,CAAC,EAAE;AAAA,cACpC,OAAO;AACL,iCAAiB;AAAA,cACnB;AAAA,YACF;AACA,gBAAI,kBAAkB,OAAO,UAAU,UAAU;AAC/C,kBACE,WAAW,SAAS,KACpB,WAAW,WAAW,SAAS,CAAC,EAAE,iBAAiB,GACnD;AAGA,oBAAI,YAAY,eAAe,KAAK;AACpC,oBACE,IAAI,OAAO,SAAS,MACnB,OAAO,OAAO,IAAI,CAAC,MAAM,YACxB,OAAO,IAAI,CAAC,EAAE,SAAS,eACzB;AACA,+BAAa,eAAe,OAAO,IAAI,CAAC,CAAC;AACzC,yBAAO,OAAO,IAAI,GAAG,CAAC;AAAA,gBACxB;AACA,oBACE,IAAI,MACH,OAAO,OAAO,IAAI,CAAC,MAAM,YACxB,OAAO,IAAI,CAAC,EAAE,SAAS,eACzB;AACA,8BAAY,eAAe,OAAO,IAAI,CAAC,CAAC,IAAI;AAC5C,yBAAO,OAAO,IAAI,GAAG,CAAC;AACtB;AAAA,gBACF;AACA,uBAAO,CAAC,IAAI,IAAIA,OAAM;AAAA,kBACpB;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AACA,gBAAI,MAAM,WAAW,OAAO,MAAM,YAAY,UAAU;AACtD,yBAAW,MAAM,OAAO;AAAA,YAC1B;AAAA,UACF;AAAA,QACF;AACA,QAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,cAAI,IAAI,aAAa,SAAS,IAAI,aAAa,OAAO;AACpD;AAAA,UACF;AACA,qBAAW,IAAI,MAAM;AAAA,QACvB,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}