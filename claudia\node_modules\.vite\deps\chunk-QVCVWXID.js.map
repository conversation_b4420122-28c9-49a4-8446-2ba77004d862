{"version": 3, "sources": ["../../highlight.js/lib/languages/accesslog.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\n Language: Apache Access Log\n Author: Oleg Efimov <<EMAIL>>\n Description: Apache/Nginx Access Logs\n Website: https://httpd.apache.org/docs/2.4/logs.html#accesslog\n Audit: 2020\n */\n\n/** @type LanguageFn */\nfunction accesslog(_hljs) {\n  // https://developer.mozilla.org/en-US/docs/Web/HTTP/Methods\n  const HTTP_VERBS = [\n    \"GET\",\n    \"POST\",\n    \"HEAD\",\n    \"PUT\",\n    \"DELETE\",\n    \"CONNECT\",\n    \"OPTIONS\",\n    \"PATCH\",\n    \"TRACE\"\n  ];\n  return {\n    name: 'Apache Access Log',\n    contains: [\n      // IP\n      {\n        className: 'number',\n        begin: /^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}(:\\d{1,5})?\\b/,\n        relevance: 5\n      },\n      // Other numbers\n      {\n        className: 'number',\n        begin: /\\b\\d+\\b/,\n        relevance: 0\n      },\n      // Requests\n      {\n        className: 'string',\n        begin: concat(/\"/, either(...HTTP_VERBS)),\n        end: /\"/,\n        keywords: HTTP_VERBS,\n        illegal: /\\n/,\n        relevance: 5,\n        contains: [\n          {\n            begin: /HTTP\\/[12]\\.\\d'/,\n            relevance: 5\n          }\n        ]\n      },\n      // Dates\n      {\n        className: 'string',\n        // dates must have a certain length, this prevents matching\n        // simple array accesses a[123] and [] and other common patterns\n        // found in other languages\n        begin: /\\[\\d[^\\]\\n]{8,}\\]/,\n        illegal: /\\n/,\n        relevance: 1\n      },\n      {\n        className: 'string',\n        begin: /\\[/,\n        end: /\\]/,\n        illegal: /\\n/,\n        relevance: 0\n      },\n      // User agent / relevance boost\n      {\n        className: 'string',\n        begin: /\"Mozilla\\/\\d\\.\\d \\(/,\n        end: /\"/,\n        illegal: /\\n/,\n        relevance: 3\n      },\n      // Strings\n      {\n        className: 'string',\n        begin: /\"/,\n        end: /\"/,\n        illegal: /\\n/,\n        relevance: 0\n      }\n    ]\n  };\n}\n\nmodule.exports = accesslog;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AASA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,MAAM,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;AAC5D,aAAO;AAAA,IACT;AAWA,aAAS,UAAU,OAAO;AAExB,YAAM,aAAa;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA;AAAA,UAER;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,OAAO,KAAK,OAAO,GAAG,UAAU,CAAC;AAAA,YACxC,KAAK;AAAA,YACL,UAAU;AAAA,YACV,SAAS;AAAA,YACT,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA;AAAA;AAAA;AAAA,YAIX,OAAO;AAAA,YACP,SAAS;AAAA,YACT,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,YACT,WAAW;AAAA,UACb;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,YACT,WAAW;AAAA,UACb;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,YACT,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}