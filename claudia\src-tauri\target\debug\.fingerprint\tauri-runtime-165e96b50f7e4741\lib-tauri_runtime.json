{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 9516179262046986648, "deps": [[442785307232013896, "build_script_build", false, 14096601821455413810], [3150220818285335163, "url", false, 7821896678146667634], [4143744114649553716, "raw_window_handle", false, 4590846174750262984], [7606335748176206944, "dpi", false, 100473507458498888], [9010263965687315507, "http", false, 18131092521246687413], [9689903380558560274, "serde", false, 11564652921496065999], [10806645703491011684, "thiserror", false, 5650462710600407025], [11050281405049894993, "tauri_utils", false, 12838874062673002299], [13116089016666501665, "windows", false, 3466355342227673606], [15367738274754116744, "serde_json", false, 6550851001671471717], [16727543399706004146, "cookie", false, 17411284503189594469]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-165e96b50f7e4741\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}