{"version": 3, "sources": ["../../refractor/lang/livescript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = livescript\nlivescript.displayName = 'livescript'\nlivescript.aliases = []\nfunction livescript(Prism) {\n  Prism.languages.livescript = {\n    comment: [\n      {\n        pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?\\*\\//,\n        lookbehind: true\n      },\n      {\n        pattern: /(^|[^\\\\])#.*/,\n        lookbehind: true\n      }\n    ],\n    'interpolated-string': {\n      /* Look-behind and look-ahead prevents wrong behavior of the greedy pattern\n       * forcing it to match \"\"\"-quoted string when it would otherwise match \"-quoted first. */\n      pattern: /(^|[^\"])(\"\"\"|\")(?:\\\\[\\s\\S]|(?!\\2)[^\\\\])*\\2(?!\")/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        variable: {\n          pattern: /(^|[^\\\\])#[a-z_](?:-?[a-z]|[\\d_])*/m,\n          lookbehind: true\n        },\n        interpolation: {\n          pattern: /(^|[^\\\\])#\\{[^}]+\\}/m,\n          lookbehind: true,\n          inside: {\n            'interpolation-punctuation': {\n              pattern: /^#\\{|\\}$/,\n              alias: 'variable'\n            } // See rest below\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    },\n    string: [\n      {\n        pattern: /('''|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1/,\n        greedy: true\n      },\n      {\n        pattern: /<\\[[\\s\\S]*?\\]>/,\n        greedy: true\n      },\n      /\\\\[^\\s,;\\])}]+/\n    ],\n    regex: [\n      {\n        pattern: /\\/\\/(?:\\[[^\\r\\n\\]]*\\]|\\\\.|(?!\\/\\/)[^\\\\\\[])+\\/\\/[gimyu]{0,5}/,\n        greedy: true,\n        inside: {\n          comment: {\n            pattern: /(^|[^\\\\])#.*/,\n            lookbehind: true\n          }\n        }\n      },\n      {\n        pattern: /\\/(?:\\[[^\\r\\n\\]]*\\]|\\\\.|[^/\\\\\\r\\n\\[])+\\/[gimyu]{0,5}/,\n        greedy: true\n      }\n    ],\n    keyword: {\n      pattern:\n        /(^|(?!-).)\\b(?:break|case|catch|class|const|continue|default|do|else|extends|fallthrough|finally|for(?: ever)?|function|if|implements|it|let|loop|new|null|otherwise|own|return|super|switch|that|then|this|throw|try|unless|until|var|void|when|while|yield)(?!-)\\b/m,\n      lookbehind: true\n    },\n    'keyword-operator': {\n      pattern:\n        /(^|[^-])\\b(?:(?:delete|require|typeof)!|(?:and|by|delete|export|from|import(?: all)?|in|instanceof|is(?: not|nt)?|not|of|or|til|to|typeof|with|xor)(?!-)\\b)/m,\n      lookbehind: true,\n      alias: 'operator'\n    },\n    boolean: {\n      pattern: /(^|[^-])\\b(?:false|no|off|on|true|yes)(?!-)\\b/m,\n      lookbehind: true\n    },\n    argument: {\n      // Don't match .&. nor &&\n      pattern: /(^|(?!\\.&\\.)[^&])&(?!&)\\d*/m,\n      lookbehind: true,\n      alias: 'variable'\n    },\n    number: /\\b(?:\\d+~[\\da-z]+|\\d[\\d_]*(?:\\.\\d[\\d_]*)?(?:[a-z]\\w*)?)/i,\n    identifier: /[a-z_](?:-?[a-z]|[\\d_])*/i,\n    operator: [\n      // Spaced .\n      {\n        pattern: /( )\\.(?= )/,\n        lookbehind: true\n      }, // Full list, in order:\n      // .= .~ .. ...\n      // .&. .^. .<<. .>>. .>>>.\n      // := :: ::=\n      // &&\n      // || |>\n      // < << <<< <<<<\n      // <- <-- <-! <--!\n      // <~ <~~ <~! <~~!\n      // <| <= <?\n      // > >> >= >?\n      // - -- -> -->\n      // + ++\n      // @ @@\n      // % %%\n      // * **\n      // ! != !~=\n      // !~> !~~>\n      // !-> !-->\n      // ~ ~> ~~> ~=\n      // = ==\n      // ^ ^^\n      // / ?\n      /\\.(?:[=~]|\\.\\.?)|\\.(?:[&|^]|<<|>>>?)\\.|:(?:=|:=?)|&&|\\|[|>]|<(?:<<?<?|--?!?|~~?!?|[|=?])?|>[>=?]?|-(?:->?|>)?|\\+\\+?|@@?|%%?|\\*\\*?|!(?:~?=|--?>|~?~>)?|~(?:~?>|=)?|==?|\\^\\^?|[\\/?]/\n    ],\n    punctuation: /[(){}\\[\\]|.,:;`]/\n  }\n  Prism.languages.livescript['interpolated-string'].inside[\n    'interpolation'\n  ].inside.rest = Prism.languages.livescript\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,eAAW,cAAc;AACzB,eAAW,UAAU,CAAC;AACtB,aAAS,WAAW,OAAO;AACzB,YAAM,UAAU,aAAa;AAAA,QAC3B,SAAS;AAAA,UACP;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,uBAAuB;AAAA;AAAA;AAAA,UAGrB,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,UAAU;AAAA,cACR,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA,eAAe;AAAA,cACb,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,6BAA6B;AAAA,kBAC3B,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA;AAAA,cACF;AAAA,YACF;AAAA,YACA,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN;AAAA,YACE,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL;AAAA,YACE,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,SAAS;AAAA,gBACP,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,oBAAoB;AAAA,UAClB,SACE;AAAA,UACF,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,SAAS;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,UAAU;AAAA;AAAA,UAER,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,UAAU;AAAA;AAAA,UAER;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAuBA;AAAA,QACF;AAAA,QACA,aAAa;AAAA,MACf;AACA,YAAM,UAAU,WAAW,qBAAqB,EAAE,OAChD,eACF,EAAE,OAAO,OAAO,MAAM,UAAU;AAAA,IAClC;AAAA;AAAA;", "names": []}