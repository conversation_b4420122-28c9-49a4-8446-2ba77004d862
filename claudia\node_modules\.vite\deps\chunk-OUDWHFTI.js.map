{"version": 3, "sources": ["../../refractor/lang/bicep.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = bicep\nbicep.displayName = 'bicep'\nbicep.aliases = []\nfunction bicep(Prism) {\n  // based loosely upon: https://github.com/Azure/bicep/blob/main/src/textmate/bicep.tmlanguage\n  Prism.languages.bicep = {\n    comment: [\n      {\n        // multiline comments eg /* ASDF */\n        pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n        lookbehind: true,\n        greedy: true\n      },\n      {\n        // singleline comments eg // ASDF\n        pattern: /(^|[^\\\\:])\\/\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    property: [\n      {\n        pattern: /([\\r\\n][ \\t]*)[a-z_]\\w*(?=[ \\t]*:)/i,\n        lookbehind: true\n      },\n      {\n        pattern: /([\\r\\n][ \\t]*)'(?:\\\\.|\\$(?!\\{)|[^'\\\\\\r\\n$])*'(?=[ \\t]*:)/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    string: [\n      {\n        pattern: /'''[^'][\\s\\S]*?'''/,\n        greedy: true\n      },\n      {\n        pattern: /(^|[^\\\\'])'(?:\\\\.|\\$(?!\\{)|[^'\\\\\\r\\n$])*'/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    'interpolated-string': {\n      pattern: /(^|[^\\\\'])'(?:\\\\.|\\$(?:(?!\\{)|\\{[^{}\\r\\n]*\\})|[^'\\\\\\r\\n$])*'/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /\\$\\{[^{}\\r\\n]*\\}/,\n          inside: {\n            expression: {\n              pattern: /(^\\$\\{)[\\s\\S]+(?=\\}$)/,\n              lookbehind: true\n            },\n            punctuation: /^\\$\\{|\\}$/\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    },\n    datatype: {\n      pattern: /(\\b(?:output|param)\\b[ \\t]+\\w+[ \\t]+)\\w+\\b/,\n      lookbehind: true,\n      alias: 'class-name'\n    },\n    boolean: /\\b(?:false|true)\\b/,\n    // https://github.com/Azure/bicep/blob/114a3251b4e6e30082a58729f19a8cc4e374ffa6/src/textmate/bicep.tmlanguage#L184\n    keyword:\n      /\\b(?:existing|for|if|in|module|null|output|param|resource|targetScope|var)\\b/,\n    decorator: /@\\w+\\b/,\n    function: /\\b[a-z_]\\w*(?=[ \\t]*\\()/i,\n    number: /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:E[+-]?\\d+)?/i,\n    operator:\n      /--|\\+\\+|\\*\\*=?|=>|&&=?|\\|\\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\\.{3}|\\?\\?=?|\\?\\.?|[~:]/,\n    punctuation: /[{}[\\];(),.:]/\n  }\n  Prism.languages.bicep['interpolated-string'].inside['interpolation'].inside[\n    'expression'\n  ].inside = Prism.languages.bicep\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC;AACjB,aAAS,MAAM,OAAO;AAEpB,YAAM,UAAU,QAAQ;AAAA,QACtB,SAAS;AAAA,UACP;AAAA;AAAA,YAEE,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,UACA;AAAA;AAAA,YAEE,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN;AAAA,YACE,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,QACA,uBAAuB;AAAA,UACrB,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,eAAe;AAAA,cACb,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,YAAY;AAAA,kBACV,SAAS;AAAA,kBACT,YAAY;AAAA,gBACd;AAAA,gBACA,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,SAAS;AAAA;AAAA,QAET,SACE;AAAA,QACF,WAAW;AAAA,QACX,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UACE;AAAA,QACF,aAAa;AAAA,MACf;AACA,YAAM,UAAU,MAAM,qBAAqB,EAAE,OAAO,eAAe,EAAE,OACnE,YACF,EAAE,SAAS,MAAM,UAAU;AAAA,IAC7B;AAAA;AAAA;", "names": []}