{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"win7-notifications\", \"windows-version\", \"windows7-compat\"]", "target": 11906320761866078153, "profile": 2040997289075261528, "path": 11579025272829604822, "deps": [[947818755262499932, "notify_rust", false, 7596894295755479267], [3150220818285335163, "url", false, 14171641885309381843], [5986029879202738730, "log", false, 16506617143423921061], [7849236192756901113, "build_script_build", false, 6012618645808553263], [9689903380558560274, "serde", false, 7541355907771353291], [10755362358622467486, "tauri", false, 12772755201288693272], [10806645703491011684, "thiserror", false, 5059168433112344807], [12409575957772518135, "time", false, 5797469020706748873], [12986574360607194341, "serde_repr", false, 13655891311490039776], [13208667028893622512, "rand", false, 9296476780665628483], [15367738274754116744, "serde_json", false, 17470562606684216992]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-plugin-notification-c1c02cd2122757ba\\dep-lib-tauri_plugin_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}