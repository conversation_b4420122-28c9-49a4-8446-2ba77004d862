{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 18348216721672176038, "path": 15695838419062756478, "deps": [[5103565458935487, "futures_io", false, 5742022182919947166], [1811549171721445101, "futures_channel", false, 5193517759234762027], [7013762810557009322, "futures_sink", false, 1897300475682464460], [7620660491849607393, "futures_core", false, 17347301603407158438], [10629569228670356391, "futures_util", false, 16760756696038921274], [12779779637805422465, "futures_executor", false, 18267266898568310368], [16240732885093539806, "futures_task", false, 2959107455638287698]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-5cc6ddbd5f88e4cc\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}