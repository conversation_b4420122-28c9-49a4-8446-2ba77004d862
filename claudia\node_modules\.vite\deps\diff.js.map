{"version": 3, "sources": ["../../diff/libesm/diff/base.js", "../../diff/libesm/diff/character.js", "../../diff/libesm/util/string.js", "../../diff/libesm/diff/word.js", "../../diff/libesm/util/params.js", "../../diff/libesm/diff/line.js", "../../diff/libesm/diff/sentence.js", "../../diff/libesm/diff/css.js", "../../diff/libesm/diff/json.js", "../../diff/libesm/diff/array.js", "../../diff/libesm/patch/line-endings.js", "../../diff/libesm/patch/parse.js", "../../diff/libesm/util/distance-iterator.js", "../../diff/libesm/patch/apply.js", "../../diff/libesm/patch/reverse.js", "../../diff/libesm/patch/create.js", "../../diff/libesm/convert/dmp.js", "../../diff/libesm/convert/xml.js"], "sourcesContent": ["export default class Diff {\n    diff(oldStr, newStr, \n    // Type below is not accurate/complete - see above for full possibilities - but it compiles\n    options = {}) {\n        let callback;\n        if (typeof options === 'function') {\n            callback = options;\n            options = {};\n        }\n        else if ('callback' in options) {\n            callback = options.callback;\n        }\n        // Allow subclasses to massage the input prior to running\n        const oldString = this.castInput(oldStr, options);\n        const newString = this.castInput(newStr, options);\n        const oldTokens = this.removeEmpty(this.tokenize(oldString, options));\n        const newTokens = this.removeEmpty(this.tokenize(newString, options));\n        return this.diffWithOptionsObj(oldTokens, newTokens, options, callback);\n    }\n    diffWithOptionsObj(oldTokens, newTokens, options, callback) {\n        var _a;\n        const done = (value) => {\n            value = this.postProcess(value, options);\n            if (callback) {\n                setTimeout(function () { callback(value); }, 0);\n                return undefined;\n            }\n            else {\n                return value;\n            }\n        };\n        const newLen = newTokens.length, oldLen = oldTokens.length;\n        let editLength = 1;\n        let maxEditLength = newLen + oldLen;\n        if (options.maxEditLength != null) {\n            maxEditLength = Math.min(maxEditLength, options.maxEditLength);\n        }\n        const maxExecutionTime = (_a = options.timeout) !== null && _a !== void 0 ? _a : Infinity;\n        const abortAfterTimestamp = Date.now() + maxExecutionTime;\n        const bestPath = [{ oldPos: -1, lastComponent: undefined }];\n        // Seed editLength = 0, i.e. the content starts with the same values\n        let newPos = this.extractCommon(bestPath[0], newTokens, oldTokens, 0, options);\n        if (bestPath[0].oldPos + 1 >= oldLen && newPos + 1 >= newLen) {\n            // Identity per the equality and tokenizer\n            return done(this.buildValues(bestPath[0].lastComponent, newTokens, oldTokens));\n        }\n        // Once we hit the right edge of the edit graph on some diagonal k, we can\n        // definitely reach the end of the edit graph in no more than k edits, so\n        // there's no point in considering any moves to diagonal k+1 any more (from\n        // which we're guaranteed to need at least k+1 more edits).\n        // Similarly, once we've reached the bottom of the edit graph, there's no\n        // point considering moves to lower diagonals.\n        // We record this fact by setting minDiagonalToConsider and\n        // maxDiagonalToConsider to some finite value once we've hit the edge of\n        // the edit graph.\n        // This optimization is not faithful to the original algorithm presented in\n        // Myers's paper, which instead pointlessly extends D-paths off the end of\n        // the edit graph - see page 7 of Myers's paper which notes this point\n        // explicitly and illustrates it with a diagram. This has major performance\n        // implications for some common scenarios. For instance, to compute a diff\n        // where the new text simply appends d characters on the end of the\n        // original text of length n, the true Myers algorithm will take O(n+d^2)\n        // time while this optimization needs only O(n+d) time.\n        let minDiagonalToConsider = -Infinity, maxDiagonalToConsider = Infinity;\n        // Main worker method. checks all permutations of a given edit length for acceptance.\n        const execEditLength = () => {\n            for (let diagonalPath = Math.max(minDiagonalToConsider, -editLength); diagonalPath <= Math.min(maxDiagonalToConsider, editLength); diagonalPath += 2) {\n                let basePath;\n                const removePath = bestPath[diagonalPath - 1], addPath = bestPath[diagonalPath + 1];\n                if (removePath) {\n                    // No one else is going to attempt to use this value, clear it\n                    // @ts-expect-error - perf optimisation. This type-violating value will never be read.\n                    bestPath[diagonalPath - 1] = undefined;\n                }\n                let canAdd = false;\n                if (addPath) {\n                    // what newPos will be after we do an insertion:\n                    const addPathNewPos = addPath.oldPos - diagonalPath;\n                    canAdd = addPath && 0 <= addPathNewPos && addPathNewPos < newLen;\n                }\n                const canRemove = removePath && removePath.oldPos + 1 < oldLen;\n                if (!canAdd && !canRemove) {\n                    // If this path is a terminal then prune\n                    // @ts-expect-error - perf optimisation. This type-violating value will never be read.\n                    bestPath[diagonalPath] = undefined;\n                    continue;\n                }\n                // Select the diagonal that we want to branch from. We select the prior\n                // path whose position in the old string is the farthest from the origin\n                // and does not pass the bounds of the diff graph\n                if (!canRemove || (canAdd && removePath.oldPos < addPath.oldPos)) {\n                    basePath = this.addToPath(addPath, true, false, 0, options);\n                }\n                else {\n                    basePath = this.addToPath(removePath, false, true, 1, options);\n                }\n                newPos = this.extractCommon(basePath, newTokens, oldTokens, diagonalPath, options);\n                if (basePath.oldPos + 1 >= oldLen && newPos + 1 >= newLen) {\n                    // If we have hit the end of both strings, then we are done\n                    return done(this.buildValues(basePath.lastComponent, newTokens, oldTokens)) || true;\n                }\n                else {\n                    bestPath[diagonalPath] = basePath;\n                    if (basePath.oldPos + 1 >= oldLen) {\n                        maxDiagonalToConsider = Math.min(maxDiagonalToConsider, diagonalPath - 1);\n                    }\n                    if (newPos + 1 >= newLen) {\n                        minDiagonalToConsider = Math.max(minDiagonalToConsider, diagonalPath + 1);\n                    }\n                }\n            }\n            editLength++;\n        };\n        // Performs the length of edit iteration. Is a bit fugly as this has to support the\n        // sync and async mode which is never fun. Loops over execEditLength until a value\n        // is produced, or until the edit length exceeds options.maxEditLength (if given),\n        // in which case it will return undefined.\n        if (callback) {\n            (function exec() {\n                setTimeout(function () {\n                    if (editLength > maxEditLength || Date.now() > abortAfterTimestamp) {\n                        return callback(undefined);\n                    }\n                    if (!execEditLength()) {\n                        exec();\n                    }\n                }, 0);\n            }());\n        }\n        else {\n            while (editLength <= maxEditLength && Date.now() <= abortAfterTimestamp) {\n                const ret = execEditLength();\n                if (ret) {\n                    return ret;\n                }\n            }\n        }\n    }\n    addToPath(path, added, removed, oldPosInc, options) {\n        const last = path.lastComponent;\n        if (last && !options.oneChangePerToken && last.added === added && last.removed === removed) {\n            return {\n                oldPos: path.oldPos + oldPosInc,\n                lastComponent: { count: last.count + 1, added: added, removed: removed, previousComponent: last.previousComponent }\n            };\n        }\n        else {\n            return {\n                oldPos: path.oldPos + oldPosInc,\n                lastComponent: { count: 1, added: added, removed: removed, previousComponent: last }\n            };\n        }\n    }\n    extractCommon(basePath, newTokens, oldTokens, diagonalPath, options) {\n        const newLen = newTokens.length, oldLen = oldTokens.length;\n        let oldPos = basePath.oldPos, newPos = oldPos - diagonalPath, commonCount = 0;\n        while (newPos + 1 < newLen && oldPos + 1 < oldLen && this.equals(oldTokens[oldPos + 1], newTokens[newPos + 1], options)) {\n            newPos++;\n            oldPos++;\n            commonCount++;\n            if (options.oneChangePerToken) {\n                basePath.lastComponent = { count: 1, previousComponent: basePath.lastComponent, added: false, removed: false };\n            }\n        }\n        if (commonCount && !options.oneChangePerToken) {\n            basePath.lastComponent = { count: commonCount, previousComponent: basePath.lastComponent, added: false, removed: false };\n        }\n        basePath.oldPos = oldPos;\n        return newPos;\n    }\n    equals(left, right, options) {\n        if (options.comparator) {\n            return options.comparator(left, right);\n        }\n        else {\n            return left === right\n                || (!!options.ignoreCase && left.toLowerCase() === right.toLowerCase());\n        }\n    }\n    removeEmpty(array) {\n        const ret = [];\n        for (let i = 0; i < array.length; i++) {\n            if (array[i]) {\n                ret.push(array[i]);\n            }\n        }\n        return ret;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    castInput(value, options) {\n        return value;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    tokenize(value, options) {\n        return Array.from(value);\n    }\n    join(chars) {\n        // Assumes ValueT is string, which is the case for most subclasses.\n        // When it's false, e.g. in diffArrays, this method needs to be overridden (e.g. with a no-op)\n        // Yes, the casts are verbose and ugly, because this pattern - of having the base class SORT OF\n        // assume tokens and values are strings, but not completely - is weird and janky.\n        return chars.join('');\n    }\n    postProcess(changeObjects, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    options) {\n        return changeObjects;\n    }\n    get useLongestToken() {\n        return false;\n    }\n    buildValues(lastComponent, newTokens, oldTokens) {\n        // First we convert our linked list of components in reverse order to an\n        // array in the right order:\n        const components = [];\n        let nextComponent;\n        while (lastComponent) {\n            components.push(lastComponent);\n            nextComponent = lastComponent.previousComponent;\n            delete lastComponent.previousComponent;\n            lastComponent = nextComponent;\n        }\n        components.reverse();\n        const componentLen = components.length;\n        let componentPos = 0, newPos = 0, oldPos = 0;\n        for (; componentPos < componentLen; componentPos++) {\n            const component = components[componentPos];\n            if (!component.removed) {\n                if (!component.added && this.useLongestToken) {\n                    let value = newTokens.slice(newPos, newPos + component.count);\n                    value = value.map(function (value, i) {\n                        const oldValue = oldTokens[oldPos + i];\n                        return oldValue.length > value.length ? oldValue : value;\n                    });\n                    component.value = this.join(value);\n                }\n                else {\n                    component.value = this.join(newTokens.slice(newPos, newPos + component.count));\n                }\n                newPos += component.count;\n                // Common case\n                if (!component.added) {\n                    oldPos += component.count;\n                }\n            }\n            else {\n                component.value = this.join(oldTokens.slice(oldPos, oldPos + component.count));\n                oldPos += component.count;\n            }\n        }\n        return components;\n    }\n}\n", "import Diff from './base.js';\nclass CharacterDiff extends Diff {\n}\nexport const characterDiff = new CharacterDiff();\nexport function diffChars(oldStr, newStr, options) {\n    return characterDiff.diff(oldStr, newStr, options);\n}\n", "export function longestCommonPrefix(str1, str2) {\n    let i;\n    for (i = 0; i < str1.length && i < str2.length; i++) {\n        if (str1[i] != str2[i]) {\n            return str1.slice(0, i);\n        }\n    }\n    return str1.slice(0, i);\n}\nexport function longestCommonSuffix(str1, str2) {\n    let i;\n    // Unlike longestCommonPrefix, we need a special case to handle all scenarios\n    // where we return the empty string since str1.slice(-0) will return the\n    // entire string.\n    if (!str1 || !str2 || str1[str1.length - 1] != str2[str2.length - 1]) {\n        return '';\n    }\n    for (i = 0; i < str1.length && i < str2.length; i++) {\n        if (str1[str1.length - (i + 1)] != str2[str2.length - (i + 1)]) {\n            return str1.slice(-i);\n        }\n    }\n    return str1.slice(-i);\n}\nexport function replacePrefix(string, oldPrefix, newPrefix) {\n    if (string.slice(0, oldPrefix.length) != oldPrefix) {\n        throw Error(`string ${JSON.stringify(string)} doesn't start with prefix ${JSON.stringify(oldPrefix)}; this is a bug`);\n    }\n    return newPrefix + string.slice(oldPrefix.length);\n}\nexport function replaceSuffix(string, oldSuffix, newSuffix) {\n    if (!oldSuffix) {\n        return string + newSuffix;\n    }\n    if (string.slice(-oldSuffix.length) != oldSuffix) {\n        throw Error(`string ${JSON.stringify(string)} doesn't end with suffix ${JSON.stringify(oldSuffix)}; this is a bug`);\n    }\n    return string.slice(0, -oldSuffix.length) + newSuffix;\n}\nexport function removePrefix(string, oldPrefix) {\n    return replacePrefix(string, oldPrefix, '');\n}\nexport function removeSuffix(string, oldSuffix) {\n    return replaceSuffix(string, oldSuffix, '');\n}\nexport function maximumOverlap(string1, string2) {\n    return string2.slice(0, overlapCount(string1, string2));\n}\n// Nicked from https://stackoverflow.com/a/60422853/1709587\nfunction overlapCount(a, b) {\n    // Deal with cases where the strings differ in length\n    let startA = 0;\n    if (a.length > b.length) {\n        startA = a.length - b.length;\n    }\n    let endB = b.length;\n    if (a.length < b.length) {\n        endB = a.length;\n    }\n    // Create a back-reference for each index\n    //   that should be followed in case of a mismatch.\n    //   We only need B to make these references:\n    const map = Array(endB);\n    let k = 0; // Index that lags behind j\n    map[0] = 0;\n    for (let j = 1; j < endB; j++) {\n        if (b[j] == b[k]) {\n            map[j] = map[k]; // skip over the same character (optional optimisation)\n        }\n        else {\n            map[j] = k;\n        }\n        while (k > 0 && b[j] != b[k]) {\n            k = map[k];\n        }\n        if (b[j] == b[k]) {\n            k++;\n        }\n    }\n    // Phase 2: use these references while iterating over A\n    k = 0;\n    for (let i = startA; i < a.length; i++) {\n        while (k > 0 && a[i] != b[k]) {\n            k = map[k];\n        }\n        if (a[i] == b[k]) {\n            k++;\n        }\n    }\n    return k;\n}\n/**\n * Returns true if the string consistently uses Windows line endings.\n */\nexport function hasOnlyWinLineEndings(string) {\n    return string.includes('\\r\\n') && !string.startsWith('\\n') && !string.match(/[^\\r]\\n/);\n}\n/**\n * Returns true if the string consistently uses Unix line endings.\n */\nexport function hasOnlyUnixLineEndings(string) {\n    return !string.includes('\\r\\n') && string.includes('\\n');\n}\nexport function trailingWs(string) {\n    // Yes, this looks overcomplicated and dumb - why not replace the whole function with\n    //     return string match(/\\s*$/)[0]\n    // you ask? Because:\n    // 1. the trap described at https://markamery.com/blog/quadratic-time-regexes/ would mean doing\n    //    this would cause this function to take O(n²) time in the worst case (specifically when\n    //    there is a massive run of NON-TRAILING whitespace in `string`), and\n    // 2. the fix proposed in the same blog post, of using a negative lookbehind, is incompatible\n    //    with old Safari versions that we'd like to not break if possible (see\n    //    https://github.com/kpdecker/jsdiff/pull/550)\n    // It feels absurd to do this with an explicit loop instead of a regex, but I really can't see a\n    // better way that doesn't result in broken behaviour.\n    let i;\n    for (i = string.length - 1; i >= 0; i--) {\n        if (!string[i].match(/\\s/)) {\n            break;\n        }\n    }\n    return string.substring(i + 1);\n}\nexport function leadingWs(string) {\n    // Thankfully the annoying considerations described in trailingWs don't apply here:\n    const match = string.match(/^\\s*/);\n    return match ? match[0] : '';\n}\n", "import Diff from './base.js';\nimport { longestCommonPrefix, longestCommonSuffix, replacePrefix, replaceSuffix, removePrefix, removeSuffix, maximumOverlap, leadingWs, trailingWs } from '../util/string.js';\n// Based on https://en.wikipedia.org/wiki/Latin_script_in_Unicode\n//\n// Ranges and exceptions:\n// Latin-1 Supplement, 0080–00FF\n//  - U+00D7  × Multiplication sign\n//  - U+00F7  ÷ Division sign\n// Latin Extended-A, 0100–017F\n// Latin Extended-B, 0180–024F\n// IPA Extensions, 0250–02AF\n// Spacing Modifier Letters, 02B0–02FF\n//  - U+02C7  ˇ &#711;  Caron\n//  - U+02D8  ˘ &#728;  Breve\n//  - U+02D9  ˙ &#729;  Dot Above\n//  - U+02DA  ˚ &#730;  Ring Above\n//  - U+02DB  ˛ &#731;  Ogonek\n//  - U+02DC  ˜ &#732;  Small Tilde\n//  - U+02DD  ˝ &#733;  Double Acute Accent\n// Latin Extended Additional, 1E00–1EFF\nconst extendedWordChars = 'a-zA-Z0-9_\\\\u{C0}-\\\\u{FF}\\\\u{D8}-\\\\u{F6}\\\\u{F8}-\\\\u{2C6}\\\\u{2C8}-\\\\u{2D7}\\\\u{2DE}-\\\\u{2FF}\\\\u{1E00}-\\\\u{1EFF}';\n// Each token is one of the following:\n// - A punctuation mark plus the surrounding whitespace\n// - A word plus the surrounding whitespace\n// - Pure whitespace (but only in the special case where this the entire text\n//   is just whitespace)\n//\n// We have to include surrounding whitespace in the tokens because the two\n// alternative approaches produce horribly broken results:\n// * If we just discard the whitespace, we can't fully reproduce the original\n//   text from the sequence of tokens and any attempt to render the diff will\n//   get the whitespace wrong.\n// * If we have separate tokens for whitespace, then in a typical text every\n//   second token will be a single space character. But this often results in\n//   the optimal diff between two texts being a perverse one that preserves\n//   the spaces between words but deletes and reinserts actual common words.\n//   See https://github.com/kpdecker/jsdiff/issues/160#issuecomment-1866099640\n//   for an example.\n//\n// Keeping the surrounding whitespace of course has implications for .equals\n// and .join, not just .tokenize.\n// This regex does NOT fully implement the tokenization rules described above.\n// Instead, it gives runs of whitespace their own \"token\". The tokenize method\n// then handles stitching whitespace tokens onto adjacent word or punctuation\n// tokens.\nconst tokenizeIncludingWhitespace = new RegExp(`[${extendedWordChars}]+|\\\\s+|[^${extendedWordChars}]`, 'ug');\nclass WordDiff extends Diff {\n    equals(left, right, options) {\n        if (options.ignoreCase) {\n            left = left.toLowerCase();\n            right = right.toLowerCase();\n        }\n        return left.trim() === right.trim();\n    }\n    tokenize(value, options = {}) {\n        let parts;\n        if (options.intlSegmenter) {\n            const segmenter = options.intlSegmenter;\n            if (segmenter.resolvedOptions().granularity != 'word') {\n                throw new Error('The segmenter passed must have a granularity of \"word\"');\n            }\n            parts = Array.from(segmenter.segment(value), segment => segment.segment);\n        }\n        else {\n            parts = value.match(tokenizeIncludingWhitespace) || [];\n        }\n        const tokens = [];\n        let prevPart = null;\n        parts.forEach(part => {\n            if ((/\\s/).test(part)) {\n                if (prevPart == null) {\n                    tokens.push(part);\n                }\n                else {\n                    tokens.push(tokens.pop() + part);\n                }\n            }\n            else if (prevPart != null && (/\\s/).test(prevPart)) {\n                if (tokens[tokens.length - 1] == prevPart) {\n                    tokens.push(tokens.pop() + part);\n                }\n                else {\n                    tokens.push(prevPart + part);\n                }\n            }\n            else {\n                tokens.push(part);\n            }\n            prevPart = part;\n        });\n        return tokens;\n    }\n    join(tokens) {\n        // Tokens being joined here will always have appeared consecutively in the\n        // same text, so we can simply strip off the leading whitespace from all the\n        // tokens except the first (and except any whitespace-only tokens - but such\n        // a token will always be the first and only token anyway) and then join them\n        // and the whitespace around words and punctuation will end up correct.\n        return tokens.map((token, i) => {\n            if (i == 0) {\n                return token;\n            }\n            else {\n                return token.replace((/^\\s+/), '');\n            }\n        }).join('');\n    }\n    postProcess(changes, options) {\n        if (!changes || options.oneChangePerToken) {\n            return changes;\n        }\n        let lastKeep = null;\n        // Change objects representing any insertion or deletion since the last\n        // \"keep\" change object. There can be at most one of each.\n        let insertion = null;\n        let deletion = null;\n        changes.forEach(change => {\n            if (change.added) {\n                insertion = change;\n            }\n            else if (change.removed) {\n                deletion = change;\n            }\n            else {\n                if (insertion || deletion) { // May be false at start of text\n                    dedupeWhitespaceInChangeObjects(lastKeep, deletion, insertion, change);\n                }\n                lastKeep = change;\n                insertion = null;\n                deletion = null;\n            }\n        });\n        if (insertion || deletion) {\n            dedupeWhitespaceInChangeObjects(lastKeep, deletion, insertion, null);\n        }\n        return changes;\n    }\n}\nexport const wordDiff = new WordDiff();\nexport function diffWords(oldStr, newStr, options) {\n    // This option has never been documented and never will be (it's clearer to\n    // just call `diffWordsWithSpace` directly if you need that behavior), but\n    // has existed in jsdiff for a long time, so we retain support for it here\n    // for the sake of backwards compatibility.\n    if ((options === null || options === void 0 ? void 0 : options.ignoreWhitespace) != null && !options.ignoreWhitespace) {\n        return diffWordsWithSpace(oldStr, newStr, options);\n    }\n    return wordDiff.diff(oldStr, newStr, options);\n}\nfunction dedupeWhitespaceInChangeObjects(startKeep, deletion, insertion, endKeep) {\n    // Before returning, we tidy up the leading and trailing whitespace of the\n    // change objects to eliminate cases where trailing whitespace in one object\n    // is repeated as leading whitespace in the next.\n    // Below are examples of the outcomes we want here to explain the code.\n    // I=insert, K=keep, D=delete\n    // 1. diffing 'foo bar baz' vs 'foo baz'\n    //    Prior to cleanup, we have K:'foo ' D:' bar ' K:' baz'\n    //    After cleanup, we want:   K:'foo ' D:'bar ' K:'baz'\n    //\n    // 2. Diffing 'foo bar baz' vs 'foo qux baz'\n    //    Prior to cleanup, we have K:'foo ' D:' bar ' I:' qux ' K:' baz'\n    //    After cleanup, we want K:'foo ' D:'bar' I:'qux' K:' baz'\n    //\n    // 3. Diffing 'foo\\nbar baz' vs 'foo baz'\n    //    Prior to cleanup, we have K:'foo ' D:'\\nbar ' K:' baz'\n    //    After cleanup, we want K'foo' D:'\\nbar' K:' baz'\n    //\n    // 4. Diffing 'foo baz' vs 'foo\\nbar baz'\n    //    Prior to cleanup, we have K:'foo\\n' I:'\\nbar ' K:' baz'\n    //    After cleanup, we ideally want K'foo' I:'\\nbar' K:' baz'\n    //    but don't actually manage this currently (the pre-cleanup change\n    //    objects don't contain enough information to make it possible).\n    //\n    // 5. Diffing 'foo   bar baz' vs 'foo  baz'\n    //    Prior to cleanup, we have K:'foo  ' D:'   bar ' K:'  baz'\n    //    After cleanup, we want K:'foo  ' D:' bar ' K:'baz'\n    //\n    // Our handling is unavoidably imperfect in the case where there's a single\n    // indel between keeps and the whitespace has changed. For instance, consider\n    // diffing 'foo\\tbar\\nbaz' vs 'foo baz'. Unless we create an extra change\n    // object to represent the insertion of the space character (which isn't even\n    // a token), we have no way to avoid losing information about the texts'\n    // original whitespace in the result we return. Still, we do our best to\n    // output something that will look sensible if we e.g. print it with\n    // insertions in green and deletions in red.\n    // Between two \"keep\" change objects (or before the first or after the last\n    // change object), we can have either:\n    // * A \"delete\" followed by an \"insert\"\n    // * Just an \"insert\"\n    // * Just a \"delete\"\n    // We handle the three cases separately.\n    if (deletion && insertion) {\n        const oldWsPrefix = leadingWs(deletion.value);\n        const oldWsSuffix = trailingWs(deletion.value);\n        const newWsPrefix = leadingWs(insertion.value);\n        const newWsSuffix = trailingWs(insertion.value);\n        if (startKeep) {\n            const commonWsPrefix = longestCommonPrefix(oldWsPrefix, newWsPrefix);\n            startKeep.value = replaceSuffix(startKeep.value, newWsPrefix, commonWsPrefix);\n            deletion.value = removePrefix(deletion.value, commonWsPrefix);\n            insertion.value = removePrefix(insertion.value, commonWsPrefix);\n        }\n        if (endKeep) {\n            const commonWsSuffix = longestCommonSuffix(oldWsSuffix, newWsSuffix);\n            endKeep.value = replacePrefix(endKeep.value, newWsSuffix, commonWsSuffix);\n            deletion.value = removeSuffix(deletion.value, commonWsSuffix);\n            insertion.value = removeSuffix(insertion.value, commonWsSuffix);\n        }\n    }\n    else if (insertion) {\n        // The whitespaces all reflect what was in the new text rather than\n        // the old, so we essentially have no information about whitespace\n        // insertion or deletion. We just want to dedupe the whitespace.\n        // We do that by having each change object keep its trailing\n        // whitespace and deleting duplicate leading whitespace where\n        // present.\n        if (startKeep) {\n            const ws = leadingWs(insertion.value);\n            insertion.value = insertion.value.substring(ws.length);\n        }\n        if (endKeep) {\n            const ws = leadingWs(endKeep.value);\n            endKeep.value = endKeep.value.substring(ws.length);\n        }\n        // otherwise we've got a deletion and no insertion\n    }\n    else if (startKeep && endKeep) {\n        const newWsFull = leadingWs(endKeep.value), delWsStart = leadingWs(deletion.value), delWsEnd = trailingWs(deletion.value);\n        // Any whitespace that comes straight after startKeep in both the old and\n        // new texts, assign to startKeep and remove from the deletion.\n        const newWsStart = longestCommonPrefix(newWsFull, delWsStart);\n        deletion.value = removePrefix(deletion.value, newWsStart);\n        // Any whitespace that comes straight before endKeep in both the old and\n        // new texts, and hasn't already been assigned to startKeep, assign to\n        // endKeep and remove from the deletion.\n        const newWsEnd = longestCommonSuffix(removePrefix(newWsFull, newWsStart), delWsEnd);\n        deletion.value = removeSuffix(deletion.value, newWsEnd);\n        endKeep.value = replacePrefix(endKeep.value, newWsFull, newWsEnd);\n        // If there's any whitespace from the new text that HASN'T already been\n        // assigned, assign it to the start:\n        startKeep.value = replaceSuffix(startKeep.value, newWsFull, newWsFull.slice(0, newWsFull.length - newWsEnd.length));\n    }\n    else if (endKeep) {\n        // We are at the start of the text. Preserve all the whitespace on\n        // endKeep, and just remove whitespace from the end of deletion to the\n        // extent that it overlaps with the start of endKeep.\n        const endKeepWsPrefix = leadingWs(endKeep.value);\n        const deletionWsSuffix = trailingWs(deletion.value);\n        const overlap = maximumOverlap(deletionWsSuffix, endKeepWsPrefix);\n        deletion.value = removeSuffix(deletion.value, overlap);\n    }\n    else if (startKeep) {\n        // We are at the END of the text. Preserve all the whitespace on\n        // startKeep, and just remove whitespace from the start of deletion to\n        // the extent that it overlaps with the end of startKeep.\n        const startKeepWsSuffix = trailingWs(startKeep.value);\n        const deletionWsPrefix = leadingWs(deletion.value);\n        const overlap = maximumOverlap(startKeepWsSuffix, deletionWsPrefix);\n        deletion.value = removePrefix(deletion.value, overlap);\n    }\n}\nclass WordsWithSpaceDiff extends Diff {\n    tokenize(value) {\n        // Slightly different to the tokenizeIncludingWhitespace regex used above in\n        // that this one treats each individual newline as a distinct tokens, rather\n        // than merging them into other surrounding whitespace. This was requested\n        // in https://github.com/kpdecker/jsdiff/issues/180 &\n        //    https://github.com/kpdecker/jsdiff/issues/211\n        const regex = new RegExp(`(\\\\r?\\\\n)|[${extendedWordChars}]+|[^\\\\S\\\\n\\\\r]+|[^${extendedWordChars}]`, 'ug');\n        return value.match(regex) || [];\n    }\n}\nexport const wordsWithSpaceDiff = new WordsWithSpaceDiff();\nexport function diffWordsWithSpace(oldStr, newStr, options) {\n    return wordsWithSpaceDiff.diff(oldStr, newStr, options);\n}\n", "export function generateOptions(options, defaults) {\n    if (typeof options === 'function') {\n        defaults.callback = options;\n    }\n    else if (options) {\n        for (const name in options) {\n            /* istanbul ignore else */\n            if (Object.prototype.hasOwnProperty.call(options, name)) {\n                defaults[name] = options[name];\n            }\n        }\n    }\n    return defaults;\n}\n", "import Diff from './base.js';\nimport { generateOptions } from '../util/params.js';\nclass LineDiff extends Diff {\n    constructor() {\n        super(...arguments);\n        this.tokenize = tokenize;\n    }\n    equals(left, right, options) {\n        // If we're ignoring whitespace, we need to normalise lines by stripping\n        // whitespace before checking equality. (This has an annoying interaction\n        // with newlineIsToken that requires special handling: if newlines get their\n        // own token, then we DON'T want to trim the *newline* tokens down to empty\n        // strings, since this would cause us to treat whitespace-only line content\n        // as equal to a separator between lines, which would be weird and\n        // inconsistent with the documented behavior of the options.)\n        if (options.ignoreWhitespace) {\n            if (!options.newlineIsToken || !left.includes('\\n')) {\n                left = left.trim();\n            }\n            if (!options.newlineIsToken || !right.includes('\\n')) {\n                right = right.trim();\n            }\n        }\n        else if (options.ignoreNewlineAtEof && !options.newlineIsToken) {\n            if (left.endsWith('\\n')) {\n                left = left.slice(0, -1);\n            }\n            if (right.endsWith('\\n')) {\n                right = right.slice(0, -1);\n            }\n        }\n        return super.equals(left, right, options);\n    }\n}\nexport const lineDiff = new LineDiff();\nexport function diffLines(oldStr, newStr, options) {\n    return lineDiff.diff(oldStr, newStr, options);\n}\nexport function diffTrimmedLines(oldStr, newStr, options) {\n    options = generateOptions(options, { ignoreWhitespace: true });\n    return lineDiff.diff(oldStr, newStr, options);\n}\n// Exported standalone so it can be used from jsonDiff too.\nexport function tokenize(value, options) {\n    if (options.stripTrailingCr) {\n        // remove one \\r before \\n to match GNU diff's --strip-trailing-cr behavior\n        value = value.replace(/\\r\\n/g, '\\n');\n    }\n    const retLines = [], linesAndNewlines = value.split(/(\\n|\\r\\n)/);\n    // Ignore the final empty token that occurs if the string ends with a new line\n    if (!linesAndNewlines[linesAndNewlines.length - 1]) {\n        linesAndNewlines.pop();\n    }\n    // Merge the content and line separators into single tokens\n    for (let i = 0; i < linesAndNewlines.length; i++) {\n        const line = linesAndNewlines[i];\n        if (i % 2 && !options.newlineIsToken) {\n            retLines[retLines.length - 1] += line;\n        }\n        else {\n            retLines.push(line);\n        }\n    }\n    return retLines;\n}\n", "import Diff from './base.js';\nfunction isSentenceEndPunct(char) {\n    return char == '.' || char == '!' || char == '?';\n}\nclass SentenceDiff extends Diff {\n    tokenize(value) {\n        var _a;\n        // If in future we drop support for environments that don't support lookbehinds, we can replace\n        // this entire function with:\n        //     return value.split(/(?<=[.!?])(\\s+|$)/);\n        // but until then, for similar reasons to the trailingWs function in string.ts, we are forced\n        // to do this verbosely \"by hand\" instead of using a regex.\n        const result = [];\n        let tokenStartI = 0;\n        for (let i = 0; i < value.length; i++) {\n            if (i == value.length - 1) {\n                result.push(value.slice(tokenStartI));\n                break;\n            }\n            if (isSentenceEndPunct(value[i]) && value[i + 1].match(/\\s/)) {\n                // We've hit a sentence break - i.e. a punctuation mark followed by whitespace.\n                // We now want to push TWO tokens to the result:\n                // 1. the sentence\n                result.push(value.slice(tokenStartI, i + 1));\n                // 2. the whitespace\n                i = tokenStartI = i + 1;\n                while ((_a = value[i + 1]) === null || _a === void 0 ? void 0 : _a.match(/\\s/)) {\n                    i++;\n                }\n                result.push(value.slice(tokenStartI, i + 1));\n                // Then the next token (a sentence) starts on the character after the whitespace.\n                // (It's okay if this is off the end of the string - then the outer loop will terminate\n                // here anyway.)\n                tokenStartI = i + 1;\n            }\n        }\n        return result;\n    }\n}\nexport const sentenceDiff = new SentenceDiff();\nexport function diffSentences(oldStr, newStr, options) {\n    return sentenceDiff.diff(oldStr, newStr, options);\n}\n", "import Diff from './base.js';\nclass CssDiff extends Diff {\n    tokenize(value) {\n        return value.split(/([{}:;,]|\\s+)/);\n    }\n}\nexport const cssDiff = new CssDiff();\nexport function diffCss(oldStr, newStr, options) {\n    return cssDiff.diff(oldStr, newStr, options);\n}\n", "import Diff from './base.js';\nimport { tokenize } from './line.js';\nclass JsonDiff extends Diff {\n    constructor() {\n        super(...arguments);\n        this.tokenize = tokenize;\n    }\n    get useLongestToken() {\n        // Discriminate between two lines of pretty-printed, serialized JSON where one of them has a\n        // dangling comma and the other doesn't. Turns out including the dangling comma yields the nicest output:\n        return true;\n    }\n    castInput(value, options) {\n        const { undefinedReplacement, stringifyReplacer = (k, v) => typeof v === 'undefined' ? undefinedReplacement : v } = options;\n        return typeof value === 'string' ? value : JSON.stringify(canonicalize(value, null, null, stringifyReplacer), null, '  ');\n    }\n    equals(left, right, options) {\n        return super.equals(left.replace(/,([\\r\\n])/g, '$1'), right.replace(/,([\\r\\n])/g, '$1'), options);\n    }\n}\nexport const jsonDiff = new JsonDiff();\nexport function diffJson(oldStr, newStr, options) {\n    return jsonDiff.diff(oldStr, newStr, options);\n}\n// This function handles the presence of circular references by bailing out when encountering an\n// object that is already on the \"stack\" of items being processed. Accepts an optional replacer\nexport function canonicalize(obj, stack, replacementStack, replacer, key) {\n    stack = stack || [];\n    replacementStack = replacementStack || [];\n    if (replacer) {\n        obj = replacer(key === undefined ? '' : key, obj);\n    }\n    let i;\n    for (i = 0; i < stack.length; i += 1) {\n        if (stack[i] === obj) {\n            return replacementStack[i];\n        }\n    }\n    let canonicalizedObj;\n    if ('[object Array]' === Object.prototype.toString.call(obj)) {\n        stack.push(obj);\n        canonicalizedObj = new Array(obj.length);\n        replacementStack.push(canonicalizedObj);\n        for (i = 0; i < obj.length; i += 1) {\n            canonicalizedObj[i] = canonicalize(obj[i], stack, replacementStack, replacer, String(i));\n        }\n        stack.pop();\n        replacementStack.pop();\n        return canonicalizedObj;\n    }\n    if (obj && obj.toJSON) {\n        obj = obj.toJSON();\n    }\n    if (typeof obj === 'object' && obj !== null) {\n        stack.push(obj);\n        canonicalizedObj = {};\n        replacementStack.push(canonicalizedObj);\n        const sortedKeys = [];\n        let key;\n        for (key in obj) {\n            /* istanbul ignore else */\n            if (Object.prototype.hasOwnProperty.call(obj, key)) {\n                sortedKeys.push(key);\n            }\n        }\n        sortedKeys.sort();\n        for (i = 0; i < sortedKeys.length; i += 1) {\n            key = sortedKeys[i];\n            canonicalizedObj[key] = canonicalize(obj[key], stack, replacementStack, replacer, key);\n        }\n        stack.pop();\n        replacementStack.pop();\n    }\n    else {\n        canonicalizedObj = obj;\n    }\n    return canonicalizedObj;\n}\n", "import Diff from './base.js';\nclass ArrayDiff extends Diff {\n    tokenize(value) {\n        return value.slice();\n    }\n    join(value) {\n        return value;\n    }\n    removeEmpty(value) {\n        return value;\n    }\n}\nexport const arrayDiff = new ArrayDiff();\nexport function diffArrays(oldArr, newArr, options) {\n    return arrayDiff.diff(oldArr, newArr, options);\n}\n", "export function unixToWin(patch) {\n    if (Array.isArray(patch)) {\n        // It would be cleaner if instead of the line below we could just write\n        //     return patch.map(unixToWin)\n        // but mysteriously TypeScript (v5.7.3 at the time of writing) does not like this and it will\n        // refuse to compile, thinking that unixToWin could then return StructuredPatch[][] and the\n        // result would be incompatible with the overload signatures.\n        // See bug report at https://github.com/microsoft/TypeScript/issues/61398.\n        return patch.map(p => unixToWin(p));\n    }\n    return Object.assign(Object.assign({}, patch), { hunks: patch.hunks.map(hunk => (Object.assign(Object.assign({}, hunk), { lines: hunk.lines.map((line, i) => {\n                var _a;\n                return (line.startsWith('\\\\') || line.endsWith('\\r') || ((_a = hunk.lines[i + 1]) === null || _a === void 0 ? void 0 : _a.startsWith('\\\\')))\n                    ? line\n                    : line + '\\r';\n            }) }))) });\n}\nexport function winToUnix(patch) {\n    if (Array.isArray(patch)) {\n        // (See comment above equivalent line in unixToWin)\n        return patch.map(p => winToUnix(p));\n    }\n    return Object.assign(Object.assign({}, patch), { hunks: patch.hunks.map(hunk => (Object.assign(Object.assign({}, hunk), { lines: hunk.lines.map(line => line.endsWith('\\r') ? line.substring(0, line.length - 1) : line) }))) });\n}\n/**\n * Returns true if the patch consistently uses Unix line endings (or only involves one line and has\n * no line endings).\n */\nexport function isUnix(patch) {\n    if (!Array.isArray(patch)) {\n        patch = [patch];\n    }\n    return !patch.some(index => index.hunks.some(hunk => hunk.lines.some(line => !line.startsWith('\\\\') && line.endsWith('\\r'))));\n}\n/**\n * Returns true if the patch uses Windows line endings and only Windows line endings.\n */\nexport function isWin(patch) {\n    if (!Array.isArray(patch)) {\n        patch = [patch];\n    }\n    return patch.some(index => index.hunks.some(hunk => hunk.lines.some(line => line.endsWith('\\r'))))\n        && patch.every(index => index.hunks.every(hunk => hunk.lines.every((line, i) => { var _a; return line.startsWith('\\\\') || line.endsWith('\\r') || ((_a = hunk.lines[i + 1]) === null || _a === void 0 ? void 0 : _a.startsWith('\\\\')); })));\n}\n", "/**\n * Parses a patch into structured data, in the same structure returned by `structuredPatch`.\n *\n * @return a JSON object representation of the a patch, suitable for use with the `applyPatch` method.\n */\nexport function parsePatch(uniDiff) {\n    const diffstr = uniDiff.split(/\\n/), list = [];\n    let i = 0;\n    function parseIndex() {\n        const index = {};\n        list.push(index);\n        // Parse diff metadata\n        while (i < diffstr.length) {\n            const line = diffstr[i];\n            // File header found, end parsing diff metadata\n            if ((/^(---|\\+\\+\\+|@@)\\s/).test(line)) {\n                break;\n            }\n            // Diff index\n            const header = (/^(?:Index:|diff(?: -r \\w+)+)\\s+(.+?)\\s*$/).exec(line);\n            if (header) {\n                index.index = header[1];\n            }\n            i++;\n        }\n        // Parse file headers if they are defined. Unified diff requires them, but\n        // there's no technical issues to have an isolated hunk without file header\n        parseFileHeader(index);\n        parseFileHeader(index);\n        // Parse hunks\n        index.hunks = [];\n        while (i < diffstr.length) {\n            const line = diffstr[i];\n            if ((/^(Index:\\s|diff\\s|---\\s|\\+\\+\\+\\s|===================================================================)/).test(line)) {\n                break;\n            }\n            else if ((/^@@/).test(line)) {\n                index.hunks.push(parseHunk());\n            }\n            else if (line) {\n                throw new Error('Unknown line ' + (i + 1) + ' ' + JSON.stringify(line));\n            }\n            else {\n                i++;\n            }\n        }\n    }\n    // Parses the --- and +++ headers, if none are found, no lines\n    // are consumed.\n    function parseFileHeader(index) {\n        const fileHeader = (/^(---|\\+\\+\\+)\\s+(.*)\\r?$/).exec(diffstr[i]);\n        if (fileHeader) {\n            const data = fileHeader[2].split('\\t', 2), header = (data[1] || '').trim();\n            let fileName = data[0].replace(/\\\\\\\\/g, '\\\\');\n            if ((/^\".*\"$/).test(fileName)) {\n                fileName = fileName.substr(1, fileName.length - 2);\n            }\n            if (fileHeader[1] === '---') {\n                index.oldFileName = fileName;\n                index.oldHeader = header;\n            }\n            else {\n                index.newFileName = fileName;\n                index.newHeader = header;\n            }\n            i++;\n        }\n    }\n    // Parses a hunk\n    // This assumes that we are at the start of a hunk.\n    function parseHunk() {\n        var _a;\n        const chunkHeaderIndex = i, chunkHeaderLine = diffstr[i++], chunkHeader = chunkHeaderLine.split(/@@ -(\\d+)(?:,(\\d+))? \\+(\\d+)(?:,(\\d+))? @@/);\n        const hunk = {\n            oldStart: +chunkHeader[1],\n            oldLines: typeof chunkHeader[2] === 'undefined' ? 1 : +chunkHeader[2],\n            newStart: +chunkHeader[3],\n            newLines: typeof chunkHeader[4] === 'undefined' ? 1 : +chunkHeader[4],\n            lines: []\n        };\n        // Unified Diff Format quirk: If the chunk size is 0,\n        // the first number is one lower than one would expect.\n        // https://www.artima.com/weblogs/viewpost.jsp?thread=164293\n        if (hunk.oldLines === 0) {\n            hunk.oldStart += 1;\n        }\n        if (hunk.newLines === 0) {\n            hunk.newStart += 1;\n        }\n        let addCount = 0, removeCount = 0;\n        for (; i < diffstr.length && (removeCount < hunk.oldLines || addCount < hunk.newLines || ((_a = diffstr[i]) === null || _a === void 0 ? void 0 : _a.startsWith('\\\\'))); i++) {\n            const operation = (diffstr[i].length == 0 && i != (diffstr.length - 1)) ? ' ' : diffstr[i][0];\n            if (operation === '+' || operation === '-' || operation === ' ' || operation === '\\\\') {\n                hunk.lines.push(diffstr[i]);\n                if (operation === '+') {\n                    addCount++;\n                }\n                else if (operation === '-') {\n                    removeCount++;\n                }\n                else if (operation === ' ') {\n                    addCount++;\n                    removeCount++;\n                }\n            }\n            else {\n                throw new Error(`Hunk at line ${chunkHeaderIndex + 1} contained invalid line ${diffstr[i]}`);\n            }\n        }\n        // Handle the empty block count case\n        if (!addCount && hunk.newLines === 1) {\n            hunk.newLines = 0;\n        }\n        if (!removeCount && hunk.oldLines === 1) {\n            hunk.oldLines = 0;\n        }\n        // Perform sanity checking\n        if (addCount !== hunk.newLines) {\n            throw new Error('Added line count did not match for hunk at line ' + (chunkHeaderIndex + 1));\n        }\n        if (removeCount !== hunk.oldLines) {\n            throw new Error('Removed line count did not match for hunk at line ' + (chunkHeaderIndex + 1));\n        }\n        return hunk;\n    }\n    while (i < diffstr.length) {\n        parseIndex();\n    }\n    return list;\n}\n", "// Iterator that traverses in the range of [min, max], stepping\n// by distance from a given start position. I.e. for [0, 4], with\n// start of 2, this will iterate 2, 3, 1, 4, 0.\nexport default function (start, minLine, maxLine) {\n    let wantForward = true, backwardExhausted = false, forwardExhausted = false, localOffset = 1;\n    return function iterator() {\n        if (wantForward && !forwardExhausted) {\n            if (backwardExhausted) {\n                localOffset++;\n            }\n            else {\n                wantForward = false;\n            }\n            // Check if trying to fit beyond text length, and if not, check it fits\n            // after offset location (or desired location on first iteration)\n            if (start + localOffset <= maxLine) {\n                return start + localOffset;\n            }\n            forwardExhausted = true;\n        }\n        if (!backwardExhausted) {\n            if (!forwardExhausted) {\n                wantForward = true;\n            }\n            // Check if trying to fit before text beginning, and if not, check it fits\n            // before offset location\n            if (minLine <= start - localOffset) {\n                return start - localOffset++;\n            }\n            backwardExhausted = true;\n            return iterator();\n        }\n        // We tried to fit hunk before text beginning and beyond text length, then\n        // hunk can't fit on the text. Return undefined\n        return undefined;\n    };\n}\n", "import { hasOnlyWinLineEndings, hasOnlyUnixLineEndings } from '../util/string.js';\nimport { isWin, isUnix, unixToWin, winToUnix } from './line-endings.js';\nimport { parsePatch } from './parse.js';\nimport distanceIterator from '../util/distance-iterator.js';\n/**\n * attempts to apply a unified diff patch.\n *\n * Hunks are applied first to last.\n * `applyPatch` first tries to apply the first hunk at the line number specified in the hunk header, and with all context lines matching exactly.\n * If that fails, it tries scanning backwards and forwards, one line at a time, to find a place to apply the hunk where the context lines match exactly.\n * If that still fails, and `fuzzFactor` is greater than zero, it increments the maximum number of mismatches (missing, extra, or changed context lines) that there can be between the hunk context and a region where we are trying to apply the patch such that the hunk will still be considered to match.\n * Regardless of `fuzzFactor`, lines to be deleted in the hunk *must* be present for a hunk to match, and the context lines *immediately* before and after an insertion must match exactly.\n *\n * Once a hunk is successfully fitted, the process begins again with the next hunk.\n * Regardless of `fuzzFactor`, later hunks must be applied later in the file than earlier hunks.\n *\n * If a hunk cannot be successfully fitted *anywhere* with fewer than `fuzzFactor` mismatches, `applyPatch` fails and returns `false`.\n *\n * If a hunk is successfully fitted but not at the line number specified by the hunk header, all subsequent hunks have their target line number adjusted accordingly.\n * (e.g. if the first hunk is applied 10 lines below where the hunk header said it should fit, `applyPatch` will *start* looking for somewhere to apply the second hunk 10 lines below where its hunk header says it goes.)\n *\n * If the patch was applied successfully, returns a string containing the patched text.\n * If the patch could not be applied (because some hunks in the patch couldn't be fitted to the text in `source`), `applyPatch` returns false.\n *\n * @param patch a string diff or the output from the `parsePatch` or `structuredPatch` methods.\n */\nexport function applyPatch(source, patch, options = {}) {\n    let patches;\n    if (typeof patch === 'string') {\n        patches = parsePatch(patch);\n    }\n    else if (Array.isArray(patch)) {\n        patches = patch;\n    }\n    else {\n        patches = [patch];\n    }\n    if (patches.length > 1) {\n        throw new Error('applyPatch only works with a single input.');\n    }\n    return applyStructuredPatch(source, patches[0], options);\n}\nfunction applyStructuredPatch(source, patch, options = {}) {\n    if (options.autoConvertLineEndings || options.autoConvertLineEndings == null) {\n        if (hasOnlyWinLineEndings(source) && isUnix(patch)) {\n            patch = unixToWin(patch);\n        }\n        else if (hasOnlyUnixLineEndings(source) && isWin(patch)) {\n            patch = winToUnix(patch);\n        }\n    }\n    // Apply the diff to the input\n    const lines = source.split('\\n'), hunks = patch.hunks, compareLine = options.compareLine || ((lineNumber, line, operation, patchContent) => line === patchContent), fuzzFactor = options.fuzzFactor || 0;\n    let minLine = 0;\n    if (fuzzFactor < 0 || !Number.isInteger(fuzzFactor)) {\n        throw new Error('fuzzFactor must be a non-negative integer');\n    }\n    // Special case for empty patch.\n    if (!hunks.length) {\n        return source;\n    }\n    // Before anything else, handle EOFNL insertion/removal. If the patch tells us to make a change\n    // to the EOFNL that is redundant/impossible - i.e. to remove a newline that's not there, or add a\n    // newline that already exists - then we either return false and fail to apply the patch (if\n    // fuzzFactor is 0) or simply ignore the problem and do nothing (if fuzzFactor is >0).\n    // If we do need to remove/add a newline at EOF, this will always be in the final hunk:\n    let prevLine = '', removeEOFNL = false, addEOFNL = false;\n    for (let i = 0; i < hunks[hunks.length - 1].lines.length; i++) {\n        const line = hunks[hunks.length - 1].lines[i];\n        if (line[0] == '\\\\') {\n            if (prevLine[0] == '+') {\n                removeEOFNL = true;\n            }\n            else if (prevLine[0] == '-') {\n                addEOFNL = true;\n            }\n        }\n        prevLine = line;\n    }\n    if (removeEOFNL) {\n        if (addEOFNL) {\n            // This means the final line gets changed but doesn't have a trailing newline in either the\n            // original or patched version. In that case, we do nothing if fuzzFactor > 0, and if\n            // fuzzFactor is 0, we simply validate that the source file has no trailing newline.\n            if (!fuzzFactor && lines[lines.length - 1] == '') {\n                return false;\n            }\n        }\n        else if (lines[lines.length - 1] == '') {\n            lines.pop();\n        }\n        else if (!fuzzFactor) {\n            return false;\n        }\n    }\n    else if (addEOFNL) {\n        if (lines[lines.length - 1] != '') {\n            lines.push('');\n        }\n        else if (!fuzzFactor) {\n            return false;\n        }\n    }\n    /**\n     * Checks if the hunk can be made to fit at the provided location with at most `maxErrors`\n     * insertions, substitutions, or deletions, while ensuring also that:\n     * - lines deleted in the hunk match exactly, and\n     * - wherever an insertion operation or block of insertion operations appears in the hunk, the\n     *   immediately preceding and following lines of context match exactly\n     *\n     * `toPos` should be set such that lines[toPos] is meant to match hunkLines[0].\n     *\n     * If the hunk can be applied, returns an object with properties `oldLineLastI` and\n     * `replacementLines`. Otherwise, returns null.\n     */\n    function applyHunk(hunkLines, toPos, maxErrors, hunkLinesI = 0, lastContextLineMatched = true, patchedLines = [], patchedLinesLength = 0) {\n        let nConsecutiveOldContextLines = 0;\n        let nextContextLineMustMatch = false;\n        for (; hunkLinesI < hunkLines.length; hunkLinesI++) {\n            const hunkLine = hunkLines[hunkLinesI], operation = (hunkLine.length > 0 ? hunkLine[0] : ' '), content = (hunkLine.length > 0 ? hunkLine.substr(1) : hunkLine);\n            if (operation === '-') {\n                if (compareLine(toPos + 1, lines[toPos], operation, content)) {\n                    toPos++;\n                    nConsecutiveOldContextLines = 0;\n                }\n                else {\n                    if (!maxErrors || lines[toPos] == null) {\n                        return null;\n                    }\n                    patchedLines[patchedLinesLength] = lines[toPos];\n                    return applyHunk(hunkLines, toPos + 1, maxErrors - 1, hunkLinesI, false, patchedLines, patchedLinesLength + 1);\n                }\n            }\n            if (operation === '+') {\n                if (!lastContextLineMatched) {\n                    return null;\n                }\n                patchedLines[patchedLinesLength] = content;\n                patchedLinesLength++;\n                nConsecutiveOldContextLines = 0;\n                nextContextLineMustMatch = true;\n            }\n            if (operation === ' ') {\n                nConsecutiveOldContextLines++;\n                patchedLines[patchedLinesLength] = lines[toPos];\n                if (compareLine(toPos + 1, lines[toPos], operation, content)) {\n                    patchedLinesLength++;\n                    lastContextLineMatched = true;\n                    nextContextLineMustMatch = false;\n                    toPos++;\n                }\n                else {\n                    if (nextContextLineMustMatch || !maxErrors) {\n                        return null;\n                    }\n                    // Consider 3 possibilities in sequence:\n                    // 1. lines contains a *substitution* not included in the patch context, or\n                    // 2. lines contains an *insertion* not included in the patch context, or\n                    // 3. lines contains a *deletion* not included in the patch context\n                    // The first two options are of course only possible if the line from lines is non-null -\n                    // i.e. only option 3 is possible if we've overrun the end of the old file.\n                    return (lines[toPos] && (applyHunk(hunkLines, toPos + 1, maxErrors - 1, hunkLinesI + 1, false, patchedLines, patchedLinesLength + 1) || applyHunk(hunkLines, toPos + 1, maxErrors - 1, hunkLinesI, false, patchedLines, patchedLinesLength + 1)) || applyHunk(hunkLines, toPos, maxErrors - 1, hunkLinesI + 1, false, patchedLines, patchedLinesLength));\n                }\n            }\n        }\n        // Before returning, trim any unmodified context lines off the end of patchedLines and reduce\n        // toPos (and thus oldLineLastI) accordingly. This allows later hunks to be applied to a region\n        // that starts in this hunk's trailing context.\n        patchedLinesLength -= nConsecutiveOldContextLines;\n        toPos -= nConsecutiveOldContextLines;\n        patchedLines.length = patchedLinesLength;\n        return {\n            patchedLines,\n            oldLineLastI: toPos - 1\n        };\n    }\n    const resultLines = [];\n    // Search best fit offsets for each hunk based on the previous ones\n    let prevHunkOffset = 0;\n    for (let i = 0; i < hunks.length; i++) {\n        const hunk = hunks[i];\n        let hunkResult;\n        const maxLine = lines.length - hunk.oldLines + fuzzFactor;\n        let toPos;\n        for (let maxErrors = 0; maxErrors <= fuzzFactor; maxErrors++) {\n            toPos = hunk.oldStart + prevHunkOffset - 1;\n            const iterator = distanceIterator(toPos, minLine, maxLine);\n            for (; toPos !== undefined; toPos = iterator()) {\n                hunkResult = applyHunk(hunk.lines, toPos, maxErrors);\n                if (hunkResult) {\n                    break;\n                }\n            }\n            if (hunkResult) {\n                break;\n            }\n        }\n        if (!hunkResult) {\n            return false;\n        }\n        // Copy everything from the end of where we applied the last hunk to the start of this hunk\n        for (let i = minLine; i < toPos; i++) {\n            resultLines.push(lines[i]);\n        }\n        // Add the lines produced by applying the hunk:\n        for (let i = 0; i < hunkResult.patchedLines.length; i++) {\n            const line = hunkResult.patchedLines[i];\n            resultLines.push(line);\n        }\n        // Set lower text limit to end of the current hunk, so next ones don't try\n        // to fit over already patched text\n        minLine = hunkResult.oldLineLastI + 1;\n        // Note the offset between where the patch said the hunk should've applied and where we\n        // applied it, so we can adjust future hunks accordingly:\n        prevHunkOffset = toPos + 1 - hunk.oldStart;\n    }\n    // Copy over the rest of the lines from the old text\n    for (let i = minLine; i < lines.length; i++) {\n        resultLines.push(lines[i]);\n    }\n    return resultLines.join('\\n');\n}\n/**\n * applies one or more patches.\n *\n * `patch` may be either an array of structured patch objects, or a string representing a patch in unified diff format (which may patch one or more files).\n *\n * This method will iterate over the contents of the patch and apply to data provided through callbacks. The general flow for each patch index is:\n *\n * - `options.loadFile(index, callback)` is called. The caller should then load the contents of the file and then pass that to the `callback(err, data)` callback. Passing an `err` will terminate further patch execution.\n * - `options.patched(index, content, callback)` is called once the patch has been applied. `content` will be the return value from `applyPatch`. When it's ready, the caller should call `callback(err)` callback. Passing an `err` will terminate further patch execution.\n *\n * Once all patches have been applied or an error occurs, the `options.complete(err)` callback is made.\n */\nexport function applyPatches(uniDiff, options) {\n    const spDiff = typeof uniDiff === 'string' ? parsePatch(uniDiff) : uniDiff;\n    let currentIndex = 0;\n    function processIndex() {\n        const index = spDiff[currentIndex++];\n        if (!index) {\n            return options.complete();\n        }\n        options.loadFile(index, function (err, data) {\n            if (err) {\n                return options.complete(err);\n            }\n            const updatedContent = applyPatch(data, index, options);\n            options.patched(index, updatedContent, function (err) {\n                if (err) {\n                    return options.complete(err);\n                }\n                processIndex();\n            });\n        });\n    }\n    processIndex();\n}\n", "export function reversePatch(structuredPatch) {\n    if (Array.isArray(structuredPatch)) {\n        // (See comment in unixToWin for why we need the pointless-looking anonymous function here)\n        return structuredPatch.map(patch => reversePatch(patch)).reverse();\n    }\n    return Object.assign(Object.assign({}, structuredPatch), { oldFileName: structuredPatch.newFileName, oldHeader: structuredPatch.newHeader, newFileName: structuredPatch.oldFileName, newHeader: structuredPatch.oldHeader, hunks: structuredPatch.hunks.map(hunk => {\n            return {\n                oldLines: hunk.newLines,\n                oldStart: hunk.newStart,\n                newLines: hunk.oldLines,\n                newStart: hunk.oldStart,\n                lines: hunk.lines.map(l => {\n                    if (l.startsWith('-')) {\n                        return `+${l.slice(1)}`;\n                    }\n                    if (l.startsWith('+')) {\n                        return `-${l.slice(1)}`;\n                    }\n                    return l;\n                })\n            };\n        }) });\n}\n", "import { diffLines } from '../diff/line.js';\nexport function structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options) {\n    let optionsObj;\n    if (!options) {\n        optionsObj = {};\n    }\n    else if (typeof options === 'function') {\n        optionsObj = { callback: options };\n    }\n    else {\n        optionsObj = options;\n    }\n    if (typeof optionsObj.context === 'undefined') {\n        optionsObj.context = 4;\n    }\n    // We copy this into its own variable to placate TypeScript, which thinks\n    // optionsObj.context might be undefined in the callbacks below.\n    const context = optionsObj.context;\n    // @ts-expect-error (runtime check for something that is correctly a static type error)\n    if (optionsObj.newlineIsToken) {\n        throw new Error('newlineIsToken may not be used with patch-generation functions, only with diffing functions');\n    }\n    if (!optionsObj.callback) {\n        return diffLinesResultToPatch(diffLines(oldStr, newStr, optionsObj));\n    }\n    else {\n        const { callback } = optionsObj;\n        diffLines(oldStr, newStr, Object.assign(Object.assign({}, optionsObj), { callback: (diff) => {\n                const patch = diffLinesResultToPatch(diff);\n                // TypeScript is unhappy without the cast because it does not understand that `patch` may\n                // be undefined here only if `callback` is StructuredPatchCallbackAbortable:\n                callback(patch);\n            } }));\n    }\n    function diffLinesResultToPatch(diff) {\n        // STEP 1: Build up the patch with no \"\\ No newline at end of file\" lines and with the arrays\n        //         of lines containing trailing newline characters. We'll tidy up later...\n        if (!diff) {\n            return;\n        }\n        diff.push({ value: '', lines: [] }); // Append an empty value to make cleanup easier\n        function contextLines(lines) {\n            return lines.map(function (entry) { return ' ' + entry; });\n        }\n        const hunks = [];\n        let oldRangeStart = 0, newRangeStart = 0, curRange = [], oldLine = 1, newLine = 1;\n        for (let i = 0; i < diff.length; i++) {\n            const current = diff[i], lines = current.lines || splitLines(current.value);\n            current.lines = lines;\n            if (current.added || current.removed) {\n                // If we have previous context, start with that\n                if (!oldRangeStart) {\n                    const prev = diff[i - 1];\n                    oldRangeStart = oldLine;\n                    newRangeStart = newLine;\n                    if (prev) {\n                        curRange = context > 0 ? contextLines(prev.lines.slice(-context)) : [];\n                        oldRangeStart -= curRange.length;\n                        newRangeStart -= curRange.length;\n                    }\n                }\n                // Output our changes\n                for (const line of lines) {\n                    curRange.push((current.added ? '+' : '-') + line);\n                }\n                // Track the updated file position\n                if (current.added) {\n                    newLine += lines.length;\n                }\n                else {\n                    oldLine += lines.length;\n                }\n            }\n            else {\n                // Identical context lines. Track line changes\n                if (oldRangeStart) {\n                    // Close out any changes that have been output (or join overlapping)\n                    if (lines.length <= context * 2 && i < diff.length - 2) {\n                        // Overlapping\n                        for (const line of contextLines(lines)) {\n                            curRange.push(line);\n                        }\n                    }\n                    else {\n                        // end the range and output\n                        const contextSize = Math.min(lines.length, context);\n                        for (const line of contextLines(lines.slice(0, contextSize))) {\n                            curRange.push(line);\n                        }\n                        const hunk = {\n                            oldStart: oldRangeStart,\n                            oldLines: (oldLine - oldRangeStart + contextSize),\n                            newStart: newRangeStart,\n                            newLines: (newLine - newRangeStart + contextSize),\n                            lines: curRange\n                        };\n                        hunks.push(hunk);\n                        oldRangeStart = 0;\n                        newRangeStart = 0;\n                        curRange = [];\n                    }\n                }\n                oldLine += lines.length;\n                newLine += lines.length;\n            }\n        }\n        // Step 2: eliminate the trailing `\\n` from each line of each hunk, and, where needed, add\n        //         \"\\ No newline at end of file\".\n        for (const hunk of hunks) {\n            for (let i = 0; i < hunk.lines.length; i++) {\n                if (hunk.lines[i].endsWith('\\n')) {\n                    hunk.lines[i] = hunk.lines[i].slice(0, -1);\n                }\n                else {\n                    hunk.lines.splice(i + 1, 0, '\\\\ No newline at end of file');\n                    i++; // Skip the line we just added, then continue iterating\n                }\n            }\n        }\n        return {\n            oldFileName: oldFileName, newFileName: newFileName,\n            oldHeader: oldHeader, newHeader: newHeader,\n            hunks: hunks\n        };\n    }\n}\n/**\n * creates a unified diff patch.\n * @param patch either a single structured patch object (as returned by `structuredPatch`) or an array of them (as returned by `parsePatch`)\n */\nexport function formatPatch(patch) {\n    if (Array.isArray(patch)) {\n        return patch.map(formatPatch).join('\\n');\n    }\n    const ret = [];\n    if (patch.oldFileName == patch.newFileName) {\n        ret.push('Index: ' + patch.oldFileName);\n    }\n    ret.push('===================================================================');\n    ret.push('--- ' + patch.oldFileName + (typeof patch.oldHeader === 'undefined' ? '' : '\\t' + patch.oldHeader));\n    ret.push('+++ ' + patch.newFileName + (typeof patch.newHeader === 'undefined' ? '' : '\\t' + patch.newHeader));\n    for (let i = 0; i < patch.hunks.length; i++) {\n        const hunk = patch.hunks[i];\n        // Unified Diff Format quirk: If the chunk size is 0,\n        // the first number is one lower than one would expect.\n        // https://www.artima.com/weblogs/viewpost.jsp?thread=164293\n        if (hunk.oldLines === 0) {\n            hunk.oldStart -= 1;\n        }\n        if (hunk.newLines === 0) {\n            hunk.newStart -= 1;\n        }\n        ret.push('@@ -' + hunk.oldStart + ',' + hunk.oldLines\n            + ' +' + hunk.newStart + ',' + hunk.newLines\n            + ' @@');\n        for (const line of hunk.lines) {\n            ret.push(line);\n        }\n    }\n    return ret.join('\\n') + '\\n';\n}\nexport function createTwoFilesPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options) {\n    if (typeof options === 'function') {\n        options = { callback: options };\n    }\n    if (!(options === null || options === void 0 ? void 0 : options.callback)) {\n        const patchObj = structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options);\n        if (!patchObj) {\n            return;\n        }\n        return formatPatch(patchObj);\n    }\n    else {\n        const { callback } = options;\n        structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, Object.assign(Object.assign({}, options), { callback: patchObj => {\n                if (!patchObj) {\n                    callback(undefined);\n                }\n                else {\n                    callback(formatPatch(patchObj));\n                }\n            } }));\n    }\n}\nexport function createPatch(fileName, oldStr, newStr, oldHeader, newHeader, options) {\n    return createTwoFilesPatch(fileName, fileName, oldStr, newStr, oldHeader, newHeader, options);\n}\n/**\n * Split `text` into an array of lines, including the trailing newline character (where present)\n */\nfunction splitLines(text) {\n    const hasTrailingNl = text.endsWith('\\n');\n    const result = text.split('\\n').map(line => line + '\\n');\n    if (hasTrailingNl) {\n        result.pop();\n    }\n    else {\n        result.push(result.pop().slice(0, -1));\n    }\n    return result;\n}\n", "/**\n * converts a list of change objects to the format returned by Google's [diff-match-patch](https://github.com/google/diff-match-patch) library\n */\nexport function convertChangesToDMP(changes) {\n    const ret = [];\n    let change, operation;\n    for (let i = 0; i < changes.length; i++) {\n        change = changes[i];\n        if (change.added) {\n            operation = 1;\n        }\n        else if (change.removed) {\n            operation = -1;\n        }\n        else {\n            operation = 0;\n        }\n        ret.push([operation, change.value]);\n    }\n    return ret;\n}\n", "/**\n * converts a list of change objects to a serialized XML format\n */\nexport function convertChangesToXML(changes) {\n    const ret = [];\n    for (let i = 0; i < changes.length; i++) {\n        const change = changes[i];\n        if (change.added) {\n            ret.push('<ins>');\n        }\n        else if (change.removed) {\n            ret.push('<del>');\n        }\n        ret.push(escapeHTML(change.value));\n        if (change.added) {\n            ret.push('</ins>');\n        }\n        else if (change.removed) {\n            ret.push('</del>');\n        }\n    }\n    return ret.join('');\n}\nfunction escapeHTML(s) {\n    let n = s;\n    n = n.replace(/&/g, '&amp;');\n    n = n.replace(/</g, '&lt;');\n    n = n.replace(/>/g, '&gt;');\n    n = n.replace(/\"/g, '&quot;');\n    return n;\n}\n"], "mappings": ";;;AAAA,IAAqB,OAArB,MAA0B;AAAA,EACtB,KAAK,QAAQ,QAEb,UAAU,CAAC,GAAG;AACV,QAAI;AACJ,QAAI,OAAO,YAAY,YAAY;AAC/B,iBAAW;AACX,gBAAU,CAAC;AAAA,IACf,WACS,cAAc,SAAS;AAC5B,iBAAW,QAAQ;AAAA,IACvB;AAEA,UAAM,YAAY,KAAK,UAAU,QAAQ,OAAO;AAChD,UAAM,YAAY,KAAK,UAAU,QAAQ,OAAO;AAChD,UAAM,YAAY,KAAK,YAAY,KAAK,SAAS,WAAW,OAAO,CAAC;AACpE,UAAM,YAAY,KAAK,YAAY,KAAK,SAAS,WAAW,OAAO,CAAC;AACpE,WAAO,KAAK,mBAAmB,WAAW,WAAW,SAAS,QAAQ;AAAA,EAC1E;AAAA,EACA,mBAAmB,WAAW,WAAW,SAAS,UAAU;AACxD,QAAI;AACJ,UAAM,OAAO,CAAC,UAAU;AACpB,cAAQ,KAAK,YAAY,OAAO,OAAO;AACvC,UAAI,UAAU;AACV,mBAAW,WAAY;AAAE,mBAAS,KAAK;AAAA,QAAG,GAAG,CAAC;AAC9C,eAAO;AAAA,MACX,OACK;AACD,eAAO;AAAA,MACX;AAAA,IACJ;AACA,UAAM,SAAS,UAAU,QAAQ,SAAS,UAAU;AACpD,QAAI,aAAa;AACjB,QAAI,gBAAgB,SAAS;AAC7B,QAAI,QAAQ,iBAAiB,MAAM;AAC/B,sBAAgB,KAAK,IAAI,eAAe,QAAQ,aAAa;AAAA,IACjE;AACA,UAAM,oBAAoB,KAAK,QAAQ,aAAa,QAAQ,OAAO,SAAS,KAAK;AACjF,UAAM,sBAAsB,KAAK,IAAI,IAAI;AACzC,UAAM,WAAW,CAAC,EAAE,QAAQ,IAAI,eAAe,OAAU,CAAC;AAE1D,QAAI,SAAS,KAAK,cAAc,SAAS,CAAC,GAAG,WAAW,WAAW,GAAG,OAAO;AAC7E,QAAI,SAAS,CAAC,EAAE,SAAS,KAAK,UAAU,SAAS,KAAK,QAAQ;AAE1D,aAAO,KAAK,KAAK,YAAY,SAAS,CAAC,EAAE,eAAe,WAAW,SAAS,CAAC;AAAA,IACjF;AAkBA,QAAI,wBAAwB,WAAW,wBAAwB;AAE/D,UAAM,iBAAiB,MAAM;AACzB,eAAS,eAAe,KAAK,IAAI,uBAAuB,CAAC,UAAU,GAAG,gBAAgB,KAAK,IAAI,uBAAuB,UAAU,GAAG,gBAAgB,GAAG;AAClJ,YAAI;AACJ,cAAM,aAAa,SAAS,eAAe,CAAC,GAAG,UAAU,SAAS,eAAe,CAAC;AAClF,YAAI,YAAY;AAGZ,mBAAS,eAAe,CAAC,IAAI;AAAA,QACjC;AACA,YAAI,SAAS;AACb,YAAI,SAAS;AAET,gBAAM,gBAAgB,QAAQ,SAAS;AACvC,mBAAS,WAAW,KAAK,iBAAiB,gBAAgB;AAAA,QAC9D;AACA,cAAM,YAAY,cAAc,WAAW,SAAS,IAAI;AACxD,YAAI,CAAC,UAAU,CAAC,WAAW;AAGvB,mBAAS,YAAY,IAAI;AACzB;AAAA,QACJ;AAIA,YAAI,CAAC,aAAc,UAAU,WAAW,SAAS,QAAQ,QAAS;AAC9D,qBAAW,KAAK,UAAU,SAAS,MAAM,OAAO,GAAG,OAAO;AAAA,QAC9D,OACK;AACD,qBAAW,KAAK,UAAU,YAAY,OAAO,MAAM,GAAG,OAAO;AAAA,QACjE;AACA,iBAAS,KAAK,cAAc,UAAU,WAAW,WAAW,cAAc,OAAO;AACjF,YAAI,SAAS,SAAS,KAAK,UAAU,SAAS,KAAK,QAAQ;AAEvD,iBAAO,KAAK,KAAK,YAAY,SAAS,eAAe,WAAW,SAAS,CAAC,KAAK;AAAA,QACnF,OACK;AACD,mBAAS,YAAY,IAAI;AACzB,cAAI,SAAS,SAAS,KAAK,QAAQ;AAC/B,oCAAwB,KAAK,IAAI,uBAAuB,eAAe,CAAC;AAAA,UAC5E;AACA,cAAI,SAAS,KAAK,QAAQ;AACtB,oCAAwB,KAAK,IAAI,uBAAuB,eAAe,CAAC;AAAA,UAC5E;AAAA,QACJ;AAAA,MACJ;AACA;AAAA,IACJ;AAKA,QAAI,UAAU;AACV,OAAC,SAAS,OAAO;AACb,mBAAW,WAAY;AACnB,cAAI,aAAa,iBAAiB,KAAK,IAAI,IAAI,qBAAqB;AAChE,mBAAO,SAAS,MAAS;AAAA,UAC7B;AACA,cAAI,CAAC,eAAe,GAAG;AACnB,iBAAK;AAAA,UACT;AAAA,QACJ,GAAG,CAAC;AAAA,MACR,GAAE;AAAA,IACN,OACK;AACD,aAAO,cAAc,iBAAiB,KAAK,IAAI,KAAK,qBAAqB;AACrE,cAAM,MAAM,eAAe;AAC3B,YAAI,KAAK;AACL,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,UAAU,MAAM,OAAO,SAAS,WAAW,SAAS;AAChD,UAAM,OAAO,KAAK;AAClB,QAAI,QAAQ,CAAC,QAAQ,qBAAqB,KAAK,UAAU,SAAS,KAAK,YAAY,SAAS;AACxF,aAAO;AAAA,QACH,QAAQ,KAAK,SAAS;AAAA,QACtB,eAAe,EAAE,OAAO,KAAK,QAAQ,GAAG,OAAc,SAAkB,mBAAmB,KAAK,kBAAkB;AAAA,MACtH;AAAA,IACJ,OACK;AACD,aAAO;AAAA,QACH,QAAQ,KAAK,SAAS;AAAA,QACtB,eAAe,EAAE,OAAO,GAAG,OAAc,SAAkB,mBAAmB,KAAK;AAAA,MACvF;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,cAAc,UAAU,WAAW,WAAW,cAAc,SAAS;AACjE,UAAM,SAAS,UAAU,QAAQ,SAAS,UAAU;AACpD,QAAI,SAAS,SAAS,QAAQ,SAAS,SAAS,cAAc,cAAc;AAC5E,WAAO,SAAS,IAAI,UAAU,SAAS,IAAI,UAAU,KAAK,OAAO,UAAU,SAAS,CAAC,GAAG,UAAU,SAAS,CAAC,GAAG,OAAO,GAAG;AACrH;AACA;AACA;AACA,UAAI,QAAQ,mBAAmB;AAC3B,iBAAS,gBAAgB,EAAE,OAAO,GAAG,mBAAmB,SAAS,eAAe,OAAO,OAAO,SAAS,MAAM;AAAA,MACjH;AAAA,IACJ;AACA,QAAI,eAAe,CAAC,QAAQ,mBAAmB;AAC3C,eAAS,gBAAgB,EAAE,OAAO,aAAa,mBAAmB,SAAS,eAAe,OAAO,OAAO,SAAS,MAAM;AAAA,IAC3H;AACA,aAAS,SAAS;AAClB,WAAO;AAAA,EACX;AAAA,EACA,OAAO,MAAM,OAAO,SAAS;AACzB,QAAI,QAAQ,YAAY;AACpB,aAAO,QAAQ,WAAW,MAAM,KAAK;AAAA,IACzC,OACK;AACD,aAAO,SAAS,SACR,CAAC,CAAC,QAAQ,cAAc,KAAK,YAAY,MAAM,MAAM,YAAY;AAAA,IAC7E;AAAA,EACJ;AAAA,EACA,YAAY,OAAO;AACf,UAAM,MAAM,CAAC;AACb,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAI,MAAM,CAAC,GAAG;AACV,YAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MACrB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA,EAEA,UAAU,OAAO,SAAS;AACtB,WAAO;AAAA,EACX;AAAA;AAAA,EAEA,SAAS,OAAO,SAAS;AACrB,WAAO,MAAM,KAAK,KAAK;AAAA,EAC3B;AAAA,EACA,KAAK,OAAO;AAKR,WAAO,MAAM,KAAK,EAAE;AAAA,EACxB;AAAA,EACA,YAAY,eAEZ,SAAS;AACL,WAAO;AAAA,EACX;AAAA,EACA,IAAI,kBAAkB;AAClB,WAAO;AAAA,EACX;AAAA,EACA,YAAY,eAAe,WAAW,WAAW;AAG7C,UAAM,aAAa,CAAC;AACpB,QAAI;AACJ,WAAO,eAAe;AAClB,iBAAW,KAAK,aAAa;AAC7B,sBAAgB,cAAc;AAC9B,aAAO,cAAc;AACrB,sBAAgB;AAAA,IACpB;AACA,eAAW,QAAQ;AACnB,UAAM,eAAe,WAAW;AAChC,QAAI,eAAe,GAAG,SAAS,GAAG,SAAS;AAC3C,WAAO,eAAe,cAAc,gBAAgB;AAChD,YAAM,YAAY,WAAW,YAAY;AACzC,UAAI,CAAC,UAAU,SAAS;AACpB,YAAI,CAAC,UAAU,SAAS,KAAK,iBAAiB;AAC1C,cAAI,QAAQ,UAAU,MAAM,QAAQ,SAAS,UAAU,KAAK;AAC5D,kBAAQ,MAAM,IAAI,SAAUA,QAAO,GAAG;AAClC,kBAAM,WAAW,UAAU,SAAS,CAAC;AACrC,mBAAO,SAAS,SAASA,OAAM,SAAS,WAAWA;AAAA,UACvD,CAAC;AACD,oBAAU,QAAQ,KAAK,KAAK,KAAK;AAAA,QACrC,OACK;AACD,oBAAU,QAAQ,KAAK,KAAK,UAAU,MAAM,QAAQ,SAAS,UAAU,KAAK,CAAC;AAAA,QACjF;AACA,kBAAU,UAAU;AAEpB,YAAI,CAAC,UAAU,OAAO;AAClB,oBAAU,UAAU;AAAA,QACxB;AAAA,MACJ,OACK;AACD,kBAAU,QAAQ,KAAK,KAAK,UAAU,MAAM,QAAQ,SAAS,UAAU,KAAK,CAAC;AAC7E,kBAAU,UAAU;AAAA,MACxB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;;;AC3PA,IAAM,gBAAN,cAA4B,KAAK;AACjC;AACO,IAAM,gBAAgB,IAAI,cAAc;AACxC,SAAS,UAAU,QAAQ,QAAQ,SAAS;AAC/C,SAAO,cAAc,KAAK,QAAQ,QAAQ,OAAO;AACrD;;;ACNO,SAAS,oBAAoB,MAAM,MAAM;AAC5C,MAAI;AACJ,OAAK,IAAI,GAAG,IAAI,KAAK,UAAU,IAAI,KAAK,QAAQ,KAAK;AACjD,QAAI,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG;AACpB,aAAO,KAAK,MAAM,GAAG,CAAC;AAAA,IAC1B;AAAA,EACJ;AACA,SAAO,KAAK,MAAM,GAAG,CAAC;AAC1B;AACO,SAAS,oBAAoB,MAAM,MAAM;AAC5C,MAAI;AAIJ,MAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,KAAK,SAAS,CAAC,KAAK,KAAK,KAAK,SAAS,CAAC,GAAG;AAClE,WAAO;AAAA,EACX;AACA,OAAK,IAAI,GAAG,IAAI,KAAK,UAAU,IAAI,KAAK,QAAQ,KAAK;AACjD,QAAI,KAAK,KAAK,UAAU,IAAI,EAAE,KAAK,KAAK,KAAK,UAAU,IAAI,EAAE,GAAG;AAC5D,aAAO,KAAK,MAAM,CAAC,CAAC;AAAA,IACxB;AAAA,EACJ;AACA,SAAO,KAAK,MAAM,CAAC,CAAC;AACxB;AACO,SAAS,cAAc,QAAQ,WAAW,WAAW;AACxD,MAAI,OAAO,MAAM,GAAG,UAAU,MAAM,KAAK,WAAW;AAChD,UAAM,MAAM,UAAU,KAAK,UAAU,MAAM,CAAC,8BAA8B,KAAK,UAAU,SAAS,CAAC,iBAAiB;AAAA,EACxH;AACA,SAAO,YAAY,OAAO,MAAM,UAAU,MAAM;AACpD;AACO,SAAS,cAAc,QAAQ,WAAW,WAAW;AACxD,MAAI,CAAC,WAAW;AACZ,WAAO,SAAS;AAAA,EACpB;AACA,MAAI,OAAO,MAAM,CAAC,UAAU,MAAM,KAAK,WAAW;AAC9C,UAAM,MAAM,UAAU,KAAK,UAAU,MAAM,CAAC,4BAA4B,KAAK,UAAU,SAAS,CAAC,iBAAiB;AAAA,EACtH;AACA,SAAO,OAAO,MAAM,GAAG,CAAC,UAAU,MAAM,IAAI;AAChD;AACO,SAAS,aAAa,QAAQ,WAAW;AAC5C,SAAO,cAAc,QAAQ,WAAW,EAAE;AAC9C;AACO,SAAS,aAAa,QAAQ,WAAW;AAC5C,SAAO,cAAc,QAAQ,WAAW,EAAE;AAC9C;AACO,SAAS,eAAe,SAAS,SAAS;AAC7C,SAAO,QAAQ,MAAM,GAAG,aAAa,SAAS,OAAO,CAAC;AAC1D;AAEA,SAAS,aAAa,GAAG,GAAG;AAExB,MAAI,SAAS;AACb,MAAI,EAAE,SAAS,EAAE,QAAQ;AACrB,aAAS,EAAE,SAAS,EAAE;AAAA,EAC1B;AACA,MAAI,OAAO,EAAE;AACb,MAAI,EAAE,SAAS,EAAE,QAAQ;AACrB,WAAO,EAAE;AAAA,EACb;AAIA,QAAM,MAAM,MAAM,IAAI;AACtB,MAAI,IAAI;AACR,MAAI,CAAC,IAAI;AACT,WAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC3B,QAAI,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG;AACd,UAAI,CAAC,IAAI,IAAI,CAAC;AAAA,IAClB,OACK;AACD,UAAI,CAAC,IAAI;AAAA,IACb;AACA,WAAO,IAAI,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG;AAC1B,UAAI,IAAI,CAAC;AAAA,IACb;AACA,QAAI,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG;AACd;AAAA,IACJ;AAAA,EACJ;AAEA,MAAI;AACJ,WAAS,IAAI,QAAQ,IAAI,EAAE,QAAQ,KAAK;AACpC,WAAO,IAAI,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG;AAC1B,UAAI,IAAI,CAAC;AAAA,IACb;AACA,QAAI,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG;AACd;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAIO,SAAS,sBAAsB,QAAQ;AAC1C,SAAO,OAAO,SAAS,MAAM,KAAK,CAAC,OAAO,WAAW,IAAI,KAAK,CAAC,OAAO,MAAM,SAAS;AACzF;AAIO,SAAS,uBAAuB,QAAQ;AAC3C,SAAO,CAAC,OAAO,SAAS,MAAM,KAAK,OAAO,SAAS,IAAI;AAC3D;AACO,SAAS,WAAW,QAAQ;AAY/B,MAAI;AACJ,OAAK,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AACrC,QAAI,CAAC,OAAO,CAAC,EAAE,MAAM,IAAI,GAAG;AACxB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,OAAO,UAAU,IAAI,CAAC;AACjC;AACO,SAAS,UAAU,QAAQ;AAE9B,QAAM,QAAQ,OAAO,MAAM,MAAM;AACjC,SAAO,QAAQ,MAAM,CAAC,IAAI;AAC9B;;;AC3GA,IAAM,oBAAoB;AAyB1B,IAAM,8BAA8B,IAAI,OAAO,IAAI,iBAAiB,aAAa,iBAAiB,KAAK,IAAI;AAC3G,IAAM,WAAN,cAAuB,KAAK;AAAA,EACxB,OAAO,MAAM,OAAO,SAAS;AACzB,QAAI,QAAQ,YAAY;AACpB,aAAO,KAAK,YAAY;AACxB,cAAQ,MAAM,YAAY;AAAA,IAC9B;AACA,WAAO,KAAK,KAAK,MAAM,MAAM,KAAK;AAAA,EACtC;AAAA,EACA,SAAS,OAAO,UAAU,CAAC,GAAG;AAC1B,QAAI;AACJ,QAAI,QAAQ,eAAe;AACvB,YAAM,YAAY,QAAQ;AAC1B,UAAI,UAAU,gBAAgB,EAAE,eAAe,QAAQ;AACnD,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC5E;AACA,cAAQ,MAAM,KAAK,UAAU,QAAQ,KAAK,GAAG,aAAW,QAAQ,OAAO;AAAA,IAC3E,OACK;AACD,cAAQ,MAAM,MAAM,2BAA2B,KAAK,CAAC;AAAA,IACzD;AACA,UAAM,SAAS,CAAC;AAChB,QAAI,WAAW;AACf,UAAM,QAAQ,UAAQ;AAClB,UAAK,KAAM,KAAK,IAAI,GAAG;AACnB,YAAI,YAAY,MAAM;AAClB,iBAAO,KAAK,IAAI;AAAA,QACpB,OACK;AACD,iBAAO,KAAK,OAAO,IAAI,IAAI,IAAI;AAAA,QACnC;AAAA,MACJ,WACS,YAAY,QAAS,KAAM,KAAK,QAAQ,GAAG;AAChD,YAAI,OAAO,OAAO,SAAS,CAAC,KAAK,UAAU;AACvC,iBAAO,KAAK,OAAO,IAAI,IAAI,IAAI;AAAA,QACnC,OACK;AACD,iBAAO,KAAK,WAAW,IAAI;AAAA,QAC/B;AAAA,MACJ,OACK;AACD,eAAO,KAAK,IAAI;AAAA,MACpB;AACA,iBAAW;AAAA,IACf,CAAC;AACD,WAAO;AAAA,EACX;AAAA,EACA,KAAK,QAAQ;AAMT,WAAO,OAAO,IAAI,CAAC,OAAO,MAAM;AAC5B,UAAI,KAAK,GAAG;AACR,eAAO;AAAA,MACX,OACK;AACD,eAAO,MAAM,QAAS,QAAS,EAAE;AAAA,MACrC;AAAA,IACJ,CAAC,EAAE,KAAK,EAAE;AAAA,EACd;AAAA,EACA,YAAY,SAAS,SAAS;AAC1B,QAAI,CAAC,WAAW,QAAQ,mBAAmB;AACvC,aAAO;AAAA,IACX;AACA,QAAI,WAAW;AAGf,QAAI,YAAY;AAChB,QAAI,WAAW;AACf,YAAQ,QAAQ,YAAU;AACtB,UAAI,OAAO,OAAO;AACd,oBAAY;AAAA,MAChB,WACS,OAAO,SAAS;AACrB,mBAAW;AAAA,MACf,OACK;AACD,YAAI,aAAa,UAAU;AACvB,0CAAgC,UAAU,UAAU,WAAW,MAAM;AAAA,QACzE;AACA,mBAAW;AACX,oBAAY;AACZ,mBAAW;AAAA,MACf;AAAA,IACJ,CAAC;AACD,QAAI,aAAa,UAAU;AACvB,sCAAgC,UAAU,UAAU,WAAW,IAAI;AAAA,IACvE;AACA,WAAO;AAAA,EACX;AACJ;AACO,IAAM,WAAW,IAAI,SAAS;AAC9B,SAAS,UAAU,QAAQ,QAAQ,SAAS;AAK/C,OAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,qBAAqB,QAAQ,CAAC,QAAQ,kBAAkB;AACnH,WAAO,mBAAmB,QAAQ,QAAQ,OAAO;AAAA,EACrD;AACA,SAAO,SAAS,KAAK,QAAQ,QAAQ,OAAO;AAChD;AACA,SAAS,gCAAgC,WAAW,UAAU,WAAW,SAAS;AA0C9E,MAAI,YAAY,WAAW;AACvB,UAAM,cAAc,UAAU,SAAS,KAAK;AAC5C,UAAM,cAAc,WAAW,SAAS,KAAK;AAC7C,UAAM,cAAc,UAAU,UAAU,KAAK;AAC7C,UAAM,cAAc,WAAW,UAAU,KAAK;AAC9C,QAAI,WAAW;AACX,YAAM,iBAAiB,oBAAoB,aAAa,WAAW;AACnE,gBAAU,QAAQ,cAAc,UAAU,OAAO,aAAa,cAAc;AAC5E,eAAS,QAAQ,aAAa,SAAS,OAAO,cAAc;AAC5D,gBAAU,QAAQ,aAAa,UAAU,OAAO,cAAc;AAAA,IAClE;AACA,QAAI,SAAS;AACT,YAAM,iBAAiB,oBAAoB,aAAa,WAAW;AACnE,cAAQ,QAAQ,cAAc,QAAQ,OAAO,aAAa,cAAc;AACxE,eAAS,QAAQ,aAAa,SAAS,OAAO,cAAc;AAC5D,gBAAU,QAAQ,aAAa,UAAU,OAAO,cAAc;AAAA,IAClE;AAAA,EACJ,WACS,WAAW;AAOhB,QAAI,WAAW;AACX,YAAM,KAAK,UAAU,UAAU,KAAK;AACpC,gBAAU,QAAQ,UAAU,MAAM,UAAU,GAAG,MAAM;AAAA,IACzD;AACA,QAAI,SAAS;AACT,YAAM,KAAK,UAAU,QAAQ,KAAK;AAClC,cAAQ,QAAQ,QAAQ,MAAM,UAAU,GAAG,MAAM;AAAA,IACrD;AAAA,EAEJ,WACS,aAAa,SAAS;AAC3B,UAAM,YAAY,UAAU,QAAQ,KAAK,GAAG,aAAa,UAAU,SAAS,KAAK,GAAG,WAAW,WAAW,SAAS,KAAK;AAGxH,UAAM,aAAa,oBAAoB,WAAW,UAAU;AAC5D,aAAS,QAAQ,aAAa,SAAS,OAAO,UAAU;AAIxD,UAAM,WAAW,oBAAoB,aAAa,WAAW,UAAU,GAAG,QAAQ;AAClF,aAAS,QAAQ,aAAa,SAAS,OAAO,QAAQ;AACtD,YAAQ,QAAQ,cAAc,QAAQ,OAAO,WAAW,QAAQ;AAGhE,cAAU,QAAQ,cAAc,UAAU,OAAO,WAAW,UAAU,MAAM,GAAG,UAAU,SAAS,SAAS,MAAM,CAAC;AAAA,EACtH,WACS,SAAS;AAId,UAAM,kBAAkB,UAAU,QAAQ,KAAK;AAC/C,UAAM,mBAAmB,WAAW,SAAS,KAAK;AAClD,UAAM,UAAU,eAAe,kBAAkB,eAAe;AAChE,aAAS,QAAQ,aAAa,SAAS,OAAO,OAAO;AAAA,EACzD,WACS,WAAW;AAIhB,UAAM,oBAAoB,WAAW,UAAU,KAAK;AACpD,UAAM,mBAAmB,UAAU,SAAS,KAAK;AACjD,UAAM,UAAU,eAAe,mBAAmB,gBAAgB;AAClE,aAAS,QAAQ,aAAa,SAAS,OAAO,OAAO;AAAA,EACzD;AACJ;AACA,IAAM,qBAAN,cAAiC,KAAK;AAAA,EAClC,SAAS,OAAO;AAMZ,UAAM,QAAQ,IAAI,OAAO,cAAc,iBAAiB,sBAAsB,iBAAiB,KAAK,IAAI;AACxG,WAAO,MAAM,MAAM,KAAK,KAAK,CAAC;AAAA,EAClC;AACJ;AACO,IAAM,qBAAqB,IAAI,mBAAmB;AAClD,SAAS,mBAAmB,QAAQ,QAAQ,SAAS;AACxD,SAAO,mBAAmB,KAAK,QAAQ,QAAQ,OAAO;AAC1D;;;ACnRO,SAAS,gBAAgB,SAAS,UAAU;AAC/C,MAAI,OAAO,YAAY,YAAY;AAC/B,aAAS,WAAW;AAAA,EACxB,WACS,SAAS;AACd,eAAW,QAAQ,SAAS;AAExB,UAAI,OAAO,UAAU,eAAe,KAAK,SAAS,IAAI,GAAG;AACrD,iBAAS,IAAI,IAAI,QAAQ,IAAI;AAAA,MACjC;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;;;ACXA,IAAM,WAAN,cAAuB,KAAK;AAAA,EACxB,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,OAAO,MAAM,OAAO,SAAS;AAQzB,QAAI,QAAQ,kBAAkB;AAC1B,UAAI,CAAC,QAAQ,kBAAkB,CAAC,KAAK,SAAS,IAAI,GAAG;AACjD,eAAO,KAAK,KAAK;AAAA,MACrB;AACA,UAAI,CAAC,QAAQ,kBAAkB,CAAC,MAAM,SAAS,IAAI,GAAG;AAClD,gBAAQ,MAAM,KAAK;AAAA,MACvB;AAAA,IACJ,WACS,QAAQ,sBAAsB,CAAC,QAAQ,gBAAgB;AAC5D,UAAI,KAAK,SAAS,IAAI,GAAG;AACrB,eAAO,KAAK,MAAM,GAAG,EAAE;AAAA,MAC3B;AACA,UAAI,MAAM,SAAS,IAAI,GAAG;AACtB,gBAAQ,MAAM,MAAM,GAAG,EAAE;AAAA,MAC7B;AAAA,IACJ;AACA,WAAO,MAAM,OAAO,MAAM,OAAO,OAAO;AAAA,EAC5C;AACJ;AACO,IAAM,WAAW,IAAI,SAAS;AAC9B,SAAS,UAAU,QAAQ,QAAQ,SAAS;AAC/C,SAAO,SAAS,KAAK,QAAQ,QAAQ,OAAO;AAChD;AACO,SAAS,iBAAiB,QAAQ,QAAQ,SAAS;AACtD,YAAU,gBAAgB,SAAS,EAAE,kBAAkB,KAAK,CAAC;AAC7D,SAAO,SAAS,KAAK,QAAQ,QAAQ,OAAO;AAChD;AAEO,SAAS,SAAS,OAAO,SAAS;AACrC,MAAI,QAAQ,iBAAiB;AAEzB,YAAQ,MAAM,QAAQ,SAAS,IAAI;AAAA,EACvC;AACA,QAAM,WAAW,CAAC,GAAG,mBAAmB,MAAM,MAAM,WAAW;AAE/D,MAAI,CAAC,iBAAiB,iBAAiB,SAAS,CAAC,GAAG;AAChD,qBAAiB,IAAI;AAAA,EACzB;AAEA,WAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAC9C,UAAM,OAAO,iBAAiB,CAAC;AAC/B,QAAI,IAAI,KAAK,CAAC,QAAQ,gBAAgB;AAClC,eAAS,SAAS,SAAS,CAAC,KAAK;AAAA,IACrC,OACK;AACD,eAAS,KAAK,IAAI;AAAA,IACtB;AAAA,EACJ;AACA,SAAO;AACX;;;AC/DA,SAAS,mBAAmB,MAAM;AAC9B,SAAO,QAAQ,OAAO,QAAQ,OAAO,QAAQ;AACjD;AACA,IAAM,eAAN,cAA2B,KAAK;AAAA,EAC5B,SAAS,OAAO;AACZ,QAAI;AAMJ,UAAM,SAAS,CAAC;AAChB,QAAI,cAAc;AAClB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAI,KAAK,MAAM,SAAS,GAAG;AACvB,eAAO,KAAK,MAAM,MAAM,WAAW,CAAC;AACpC;AAAA,MACJ;AACA,UAAI,mBAAmB,MAAM,CAAC,CAAC,KAAK,MAAM,IAAI,CAAC,EAAE,MAAM,IAAI,GAAG;AAI1D,eAAO,KAAK,MAAM,MAAM,aAAa,IAAI,CAAC,CAAC;AAE3C,YAAI,cAAc,IAAI;AACtB,gBAAQ,KAAK,MAAM,IAAI,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM,IAAI,GAAG;AAC5E;AAAA,QACJ;AACA,eAAO,KAAK,MAAM,MAAM,aAAa,IAAI,CAAC,CAAC;AAI3C,sBAAc,IAAI;AAAA,MACtB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AACO,IAAM,eAAe,IAAI,aAAa;AACtC,SAAS,cAAc,QAAQ,QAAQ,SAAS;AACnD,SAAO,aAAa,KAAK,QAAQ,QAAQ,OAAO;AACpD;;;ACzCA,IAAM,UAAN,cAAsB,KAAK;AAAA,EACvB,SAAS,OAAO;AACZ,WAAO,MAAM,MAAM,eAAe;AAAA,EACtC;AACJ;AACO,IAAM,UAAU,IAAI,QAAQ;AAC5B,SAAS,QAAQ,QAAQ,QAAQ,SAAS;AAC7C,SAAO,QAAQ,KAAK,QAAQ,QAAQ,OAAO;AAC/C;;;ACPA,IAAM,WAAN,cAAuB,KAAK;AAAA,EACxB,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,IAAI,kBAAkB;AAGlB,WAAO;AAAA,EACX;AAAA,EACA,UAAU,OAAO,SAAS;AACtB,UAAM,EAAE,sBAAsB,oBAAoB,CAAC,GAAG,MAAM,OAAO,MAAM,cAAc,uBAAuB,EAAE,IAAI;AACpH,WAAO,OAAO,UAAU,WAAW,QAAQ,KAAK,UAAU,aAAa,OAAO,MAAM,MAAM,iBAAiB,GAAG,MAAM,IAAI;AAAA,EAC5H;AAAA,EACA,OAAO,MAAM,OAAO,SAAS;AACzB,WAAO,MAAM,OAAO,KAAK,QAAQ,cAAc,IAAI,GAAG,MAAM,QAAQ,cAAc,IAAI,GAAG,OAAO;AAAA,EACpG;AACJ;AACO,IAAM,WAAW,IAAI,SAAS;AAC9B,SAAS,SAAS,QAAQ,QAAQ,SAAS;AAC9C,SAAO,SAAS,KAAK,QAAQ,QAAQ,OAAO;AAChD;AAGO,SAAS,aAAa,KAAK,OAAO,kBAAkB,UAAU,KAAK;AACtE,UAAQ,SAAS,CAAC;AAClB,qBAAmB,oBAAoB,CAAC;AACxC,MAAI,UAAU;AACV,UAAM,SAAS,QAAQ,SAAY,KAAK,KAAK,GAAG;AAAA,EACpD;AACA,MAAI;AACJ,OAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AAClC,QAAI,MAAM,CAAC,MAAM,KAAK;AAClB,aAAO,iBAAiB,CAAC;AAAA,IAC7B;AAAA,EACJ;AACA,MAAI;AACJ,MAAI,qBAAqB,OAAO,UAAU,SAAS,KAAK,GAAG,GAAG;AAC1D,UAAM,KAAK,GAAG;AACd,uBAAmB,IAAI,MAAM,IAAI,MAAM;AACvC,qBAAiB,KAAK,gBAAgB;AACtC,SAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AAChC,uBAAiB,CAAC,IAAI,aAAa,IAAI,CAAC,GAAG,OAAO,kBAAkB,UAAU,OAAO,CAAC,CAAC;AAAA,IAC3F;AACA,UAAM,IAAI;AACV,qBAAiB,IAAI;AACrB,WAAO;AAAA,EACX;AACA,MAAI,OAAO,IAAI,QAAQ;AACnB,UAAM,IAAI,OAAO;AAAA,EACrB;AACA,MAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AACzC,UAAM,KAAK,GAAG;AACd,uBAAmB,CAAC;AACpB,qBAAiB,KAAK,gBAAgB;AACtC,UAAM,aAAa,CAAC;AACpB,QAAIC;AACJ,SAAKA,QAAO,KAAK;AAEb,UAAI,OAAO,UAAU,eAAe,KAAK,KAAKA,IAAG,GAAG;AAChD,mBAAW,KAAKA,IAAG;AAAA,MACvB;AAAA,IACJ;AACA,eAAW,KAAK;AAChB,SAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AACvC,MAAAA,OAAM,WAAW,CAAC;AAClB,uBAAiBA,IAAG,IAAI,aAAa,IAAIA,IAAG,GAAG,OAAO,kBAAkB,UAAUA,IAAG;AAAA,IACzF;AACA,UAAM,IAAI;AACV,qBAAiB,IAAI;AAAA,EACzB,OACK;AACD,uBAAmB;AAAA,EACvB;AACA,SAAO;AACX;;;AC5EA,IAAM,YAAN,cAAwB,KAAK;AAAA,EACzB,SAAS,OAAO;AACZ,WAAO,MAAM,MAAM;AAAA,EACvB;AAAA,EACA,KAAK,OAAO;AACR,WAAO;AAAA,EACX;AAAA,EACA,YAAY,OAAO;AACf,WAAO;AAAA,EACX;AACJ;AACO,IAAM,YAAY,IAAI,UAAU;AAChC,SAAS,WAAW,QAAQ,QAAQ,SAAS;AAChD,SAAO,UAAU,KAAK,QAAQ,QAAQ,OAAO;AACjD;;;ACfO,SAAS,UAAU,OAAO;AAC7B,MAAI,MAAM,QAAQ,KAAK,GAAG;AAOtB,WAAO,MAAM,IAAI,OAAK,UAAU,CAAC,CAAC;AAAA,EACtC;AACA,SAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,GAAG,EAAE,OAAO,MAAM,MAAM,IAAI,UAAS,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,OAAO,KAAK,MAAM,IAAI,CAAC,MAAM,MAAM;AACjJ,QAAI;AACJ,WAAQ,KAAK,WAAW,IAAI,KAAK,KAAK,SAAS,IAAI,OAAO,KAAK,KAAK,MAAM,IAAI,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,IAAI,KACnI,OACA,OAAO;AAAA,EACjB,CAAC,EAAE,CAAC,CAAE,EAAE,CAAC;AACrB;AACO,SAAS,UAAU,OAAO;AAC7B,MAAI,MAAM,QAAQ,KAAK,GAAG;AAEtB,WAAO,MAAM,IAAI,OAAK,UAAU,CAAC,CAAC;AAAA,EACtC;AACA,SAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,GAAG,EAAE,OAAO,MAAM,MAAM,IAAI,UAAS,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,OAAO,KAAK,MAAM,IAAI,UAAQ,KAAK,SAAS,IAAI,IAAI,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC,IAAI,IAAI,EAAE,CAAC,CAAE,EAAE,CAAC;AACnO;AAKO,SAAS,OAAO,OAAO;AAC1B,MAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACvB,YAAQ,CAAC,KAAK;AAAA,EAClB;AACA,SAAO,CAAC,MAAM,KAAK,WAAS,MAAM,MAAM,KAAK,UAAQ,KAAK,MAAM,KAAK,UAAQ,CAAC,KAAK,WAAW,IAAI,KAAK,KAAK,SAAS,IAAI,CAAC,CAAC,CAAC;AAChI;AAIO,SAAS,MAAM,OAAO;AACzB,MAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACvB,YAAQ,CAAC,KAAK;AAAA,EAClB;AACA,SAAO,MAAM,KAAK,WAAS,MAAM,MAAM,KAAK,UAAQ,KAAK,MAAM,KAAK,UAAQ,KAAK,SAAS,IAAI,CAAC,CAAC,CAAC,KAC1F,MAAM,MAAM,WAAS,MAAM,MAAM,MAAM,UAAQ,KAAK,MAAM,MAAM,CAAC,MAAM,MAAM;AAAE,QAAI;AAAI,WAAO,KAAK,WAAW,IAAI,KAAK,KAAK,SAAS,IAAI,OAAO,KAAK,KAAK,MAAM,IAAI,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,IAAI;AAAA,EAAI,CAAC,CAAC,CAAC;AACjP;;;ACtCO,SAAS,WAAW,SAAS;AAChC,QAAM,UAAU,QAAQ,MAAM,IAAI,GAAG,OAAO,CAAC;AAC7C,MAAI,IAAI;AACR,WAAS,aAAa;AAClB,UAAM,QAAQ,CAAC;AACf,SAAK,KAAK,KAAK;AAEf,WAAO,IAAI,QAAQ,QAAQ;AACvB,YAAM,OAAO,QAAQ,CAAC;AAEtB,UAAK,qBAAsB,KAAK,IAAI,GAAG;AACnC;AAAA,MACJ;AAEA,YAAM,SAAU,2CAA4C,KAAK,IAAI;AACrE,UAAI,QAAQ;AACR,cAAM,QAAQ,OAAO,CAAC;AAAA,MAC1B;AACA;AAAA,IACJ;AAGA,oBAAgB,KAAK;AACrB,oBAAgB,KAAK;AAErB,UAAM,QAAQ,CAAC;AACf,WAAO,IAAI,QAAQ,QAAQ;AACvB,YAAM,OAAO,QAAQ,CAAC;AACtB,UAAK,wGAAyG,KAAK,IAAI,GAAG;AACtH;AAAA,MACJ,WACU,MAAO,KAAK,IAAI,GAAG;AACzB,cAAM,MAAM,KAAK,UAAU,CAAC;AAAA,MAChC,WACS,MAAM;AACX,cAAM,IAAI,MAAM,mBAAmB,IAAI,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC;AAAA,MAC1E,OACK;AACD;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAGA,WAAS,gBAAgB,OAAO;AAC5B,UAAM,aAAc,2BAA4B,KAAK,QAAQ,CAAC,CAAC;AAC/D,QAAI,YAAY;AACZ,YAAM,OAAO,WAAW,CAAC,EAAE,MAAM,KAAM,CAAC,GAAG,UAAU,KAAK,CAAC,KAAK,IAAI,KAAK;AACzE,UAAI,WAAW,KAAK,CAAC,EAAE,QAAQ,SAAS,IAAI;AAC5C,UAAK,SAAU,KAAK,QAAQ,GAAG;AAC3B,mBAAW,SAAS,OAAO,GAAG,SAAS,SAAS,CAAC;AAAA,MACrD;AACA,UAAI,WAAW,CAAC,MAAM,OAAO;AACzB,cAAM,cAAc;AACpB,cAAM,YAAY;AAAA,MACtB,OACK;AACD,cAAM,cAAc;AACpB,cAAM,YAAY;AAAA,MACtB;AACA;AAAA,IACJ;AAAA,EACJ;AAGA,WAAS,YAAY;AACjB,QAAI;AACJ,UAAM,mBAAmB,GAAG,kBAAkB,QAAQ,GAAG,GAAG,cAAc,gBAAgB,MAAM,4CAA4C;AAC5I,UAAM,OAAO;AAAA,MACT,UAAU,CAAC,YAAY,CAAC;AAAA,MACxB,UAAU,OAAO,YAAY,CAAC,MAAM,cAAc,IAAI,CAAC,YAAY,CAAC;AAAA,MACpE,UAAU,CAAC,YAAY,CAAC;AAAA,MACxB,UAAU,OAAO,YAAY,CAAC,MAAM,cAAc,IAAI,CAAC,YAAY,CAAC;AAAA,MACpE,OAAO,CAAC;AAAA,IACZ;AAIA,QAAI,KAAK,aAAa,GAAG;AACrB,WAAK,YAAY;AAAA,IACrB;AACA,QAAI,KAAK,aAAa,GAAG;AACrB,WAAK,YAAY;AAAA,IACrB;AACA,QAAI,WAAW,GAAG,cAAc;AAChC,WAAO,IAAI,QAAQ,WAAW,cAAc,KAAK,YAAY,WAAW,KAAK,cAAc,KAAK,QAAQ,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,IAAI,KAAK,KAAK;AACzK,YAAM,YAAa,QAAQ,CAAC,EAAE,UAAU,KAAK,KAAM,QAAQ,SAAS,IAAM,MAAM,QAAQ,CAAC,EAAE,CAAC;AAC5F,UAAI,cAAc,OAAO,cAAc,OAAO,cAAc,OAAO,cAAc,MAAM;AACnF,aAAK,MAAM,KAAK,QAAQ,CAAC,CAAC;AAC1B,YAAI,cAAc,KAAK;AACnB;AAAA,QACJ,WACS,cAAc,KAAK;AACxB;AAAA,QACJ,WACS,cAAc,KAAK;AACxB;AACA;AAAA,QACJ;AAAA,MACJ,OACK;AACD,cAAM,IAAI,MAAM,gBAAgB,mBAAmB,CAAC,2BAA2B,QAAQ,CAAC,CAAC,EAAE;AAAA,MAC/F;AAAA,IACJ;AAEA,QAAI,CAAC,YAAY,KAAK,aAAa,GAAG;AAClC,WAAK,WAAW;AAAA,IACpB;AACA,QAAI,CAAC,eAAe,KAAK,aAAa,GAAG;AACrC,WAAK,WAAW;AAAA,IACpB;AAEA,QAAI,aAAa,KAAK,UAAU;AAC5B,YAAM,IAAI,MAAM,sDAAsD,mBAAmB,EAAE;AAAA,IAC/F;AACA,QAAI,gBAAgB,KAAK,UAAU;AAC/B,YAAM,IAAI,MAAM,wDAAwD,mBAAmB,EAAE;AAAA,IACjG;AACA,WAAO;AAAA,EACX;AACA,SAAO,IAAI,QAAQ,QAAQ;AACvB,eAAW;AAAA,EACf;AACA,SAAO;AACX;;;AC9He,SAAR,0BAAkB,OAAO,SAAS,SAAS;AAC9C,MAAI,cAAc,MAAM,oBAAoB,OAAO,mBAAmB,OAAO,cAAc;AAC3F,SAAO,SAAS,WAAW;AACvB,QAAI,eAAe,CAAC,kBAAkB;AAClC,UAAI,mBAAmB;AACnB;AAAA,MACJ,OACK;AACD,sBAAc;AAAA,MAClB;AAGA,UAAI,QAAQ,eAAe,SAAS;AAChC,eAAO,QAAQ;AAAA,MACnB;AACA,yBAAmB;AAAA,IACvB;AACA,QAAI,CAAC,mBAAmB;AACpB,UAAI,CAAC,kBAAkB;AACnB,sBAAc;AAAA,MAClB;AAGA,UAAI,WAAW,QAAQ,aAAa;AAChC,eAAO,QAAQ;AAAA,MACnB;AACA,0BAAoB;AACpB,aAAO,SAAS;AAAA,IACpB;AAGA,WAAO;AAAA,EACX;AACJ;;;ACVO,SAAS,WAAW,QAAQ,OAAO,UAAU,CAAC,GAAG;AACpD,MAAI;AACJ,MAAI,OAAO,UAAU,UAAU;AAC3B,cAAU,WAAW,KAAK;AAAA,EAC9B,WACS,MAAM,QAAQ,KAAK,GAAG;AAC3B,cAAU;AAAA,EACd,OACK;AACD,cAAU,CAAC,KAAK;AAAA,EACpB;AACA,MAAI,QAAQ,SAAS,GAAG;AACpB,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAChE;AACA,SAAO,qBAAqB,QAAQ,QAAQ,CAAC,GAAG,OAAO;AAC3D;AACA,SAAS,qBAAqB,QAAQ,OAAO,UAAU,CAAC,GAAG;AACvD,MAAI,QAAQ,0BAA0B,QAAQ,0BAA0B,MAAM;AAC1E,QAAI,sBAAsB,MAAM,KAAK,OAAO,KAAK,GAAG;AAChD,cAAQ,UAAU,KAAK;AAAA,IAC3B,WACS,uBAAuB,MAAM,KAAK,MAAM,KAAK,GAAG;AACrD,cAAQ,UAAU,KAAK;AAAA,IAC3B;AAAA,EACJ;AAEA,QAAM,QAAQ,OAAO,MAAM,IAAI,GAAG,QAAQ,MAAM,OAAO,cAAc,QAAQ,gBAAgB,CAAC,YAAY,MAAM,WAAW,iBAAiB,SAAS,eAAe,aAAa,QAAQ,cAAc;AACvM,MAAI,UAAU;AACd,MAAI,aAAa,KAAK,CAAC,OAAO,UAAU,UAAU,GAAG;AACjD,UAAM,IAAI,MAAM,2CAA2C;AAAA,EAC/D;AAEA,MAAI,CAAC,MAAM,QAAQ;AACf,WAAO;AAAA,EACX;AAMA,MAAI,WAAW,IAAI,cAAc,OAAO,WAAW;AACnD,WAAS,IAAI,GAAG,IAAI,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,KAAK;AAC3D,UAAM,OAAO,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,CAAC;AAC5C,QAAI,KAAK,CAAC,KAAK,MAAM;AACjB,UAAI,SAAS,CAAC,KAAK,KAAK;AACpB,sBAAc;AAAA,MAClB,WACS,SAAS,CAAC,KAAK,KAAK;AACzB,mBAAW;AAAA,MACf;AAAA,IACJ;AACA,eAAW;AAAA,EACf;AACA,MAAI,aAAa;AACb,QAAI,UAAU;AAIV,UAAI,CAAC,cAAc,MAAM,MAAM,SAAS,CAAC,KAAK,IAAI;AAC9C,eAAO;AAAA,MACX;AAAA,IACJ,WACS,MAAM,MAAM,SAAS,CAAC,KAAK,IAAI;AACpC,YAAM,IAAI;AAAA,IACd,WACS,CAAC,YAAY;AAClB,aAAO;AAAA,IACX;AAAA,EACJ,WACS,UAAU;AACf,QAAI,MAAM,MAAM,SAAS,CAAC,KAAK,IAAI;AAC/B,YAAM,KAAK,EAAE;AAAA,IACjB,WACS,CAAC,YAAY;AAClB,aAAO;AAAA,IACX;AAAA,EACJ;AAaA,WAAS,UAAU,WAAW,OAAO,WAAW,aAAa,GAAG,yBAAyB,MAAM,eAAe,CAAC,GAAG,qBAAqB,GAAG;AACtI,QAAI,8BAA8B;AAClC,QAAI,2BAA2B;AAC/B,WAAO,aAAa,UAAU,QAAQ,cAAc;AAChD,YAAM,WAAW,UAAU,UAAU,GAAG,YAAa,SAAS,SAAS,IAAI,SAAS,CAAC,IAAI,KAAM,UAAW,SAAS,SAAS,IAAI,SAAS,OAAO,CAAC,IAAI;AACrJ,UAAI,cAAc,KAAK;AACnB,YAAI,YAAY,QAAQ,GAAG,MAAM,KAAK,GAAG,WAAW,OAAO,GAAG;AAC1D;AACA,wCAA8B;AAAA,QAClC,OACK;AACD,cAAI,CAAC,aAAa,MAAM,KAAK,KAAK,MAAM;AACpC,mBAAO;AAAA,UACX;AACA,uBAAa,kBAAkB,IAAI,MAAM,KAAK;AAC9C,iBAAO,UAAU,WAAW,QAAQ,GAAG,YAAY,GAAG,YAAY,OAAO,cAAc,qBAAqB,CAAC;AAAA,QACjH;AAAA,MACJ;AACA,UAAI,cAAc,KAAK;AACnB,YAAI,CAAC,wBAAwB;AACzB,iBAAO;AAAA,QACX;AACA,qBAAa,kBAAkB,IAAI;AACnC;AACA,sCAA8B;AAC9B,mCAA2B;AAAA,MAC/B;AACA,UAAI,cAAc,KAAK;AACnB;AACA,qBAAa,kBAAkB,IAAI,MAAM,KAAK;AAC9C,YAAI,YAAY,QAAQ,GAAG,MAAM,KAAK,GAAG,WAAW,OAAO,GAAG;AAC1D;AACA,mCAAyB;AACzB,qCAA2B;AAC3B;AAAA,QACJ,OACK;AACD,cAAI,4BAA4B,CAAC,WAAW;AACxC,mBAAO;AAAA,UACX;AAOA,iBAAQ,MAAM,KAAK,MAAM,UAAU,WAAW,QAAQ,GAAG,YAAY,GAAG,aAAa,GAAG,OAAO,cAAc,qBAAqB,CAAC,KAAK,UAAU,WAAW,QAAQ,GAAG,YAAY,GAAG,YAAY,OAAO,cAAc,qBAAqB,CAAC,MAAM,UAAU,WAAW,OAAO,YAAY,GAAG,aAAa,GAAG,OAAO,cAAc,kBAAkB;AAAA,QAC1V;AAAA,MACJ;AAAA,IACJ;AAIA,0BAAsB;AACtB,aAAS;AACT,iBAAa,SAAS;AACtB,WAAO;AAAA,MACH;AAAA,MACA,cAAc,QAAQ;AAAA,IAC1B;AAAA,EACJ;AACA,QAAM,cAAc,CAAC;AAErB,MAAI,iBAAiB;AACrB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAM,OAAO,MAAM,CAAC;AACpB,QAAI;AACJ,UAAM,UAAU,MAAM,SAAS,KAAK,WAAW;AAC/C,QAAI;AACJ,aAAS,YAAY,GAAG,aAAa,YAAY,aAAa;AAC1D,cAAQ,KAAK,WAAW,iBAAiB;AACzC,YAAM,WAAW,0BAAiB,OAAO,SAAS,OAAO;AACzD,aAAO,UAAU,QAAW,QAAQ,SAAS,GAAG;AAC5C,qBAAa,UAAU,KAAK,OAAO,OAAO,SAAS;AACnD,YAAI,YAAY;AACZ;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,YAAY;AACZ;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAAC,YAAY;AACb,aAAO;AAAA,IACX;AAEA,aAASC,KAAI,SAASA,KAAI,OAAOA,MAAK;AAClC,kBAAY,KAAK,MAAMA,EAAC,CAAC;AAAA,IAC7B;AAEA,aAASA,KAAI,GAAGA,KAAI,WAAW,aAAa,QAAQA,MAAK;AACrD,YAAM,OAAO,WAAW,aAAaA,EAAC;AACtC,kBAAY,KAAK,IAAI;AAAA,IACzB;AAGA,cAAU,WAAW,eAAe;AAGpC,qBAAiB,QAAQ,IAAI,KAAK;AAAA,EACtC;AAEA,WAAS,IAAI,SAAS,IAAI,MAAM,QAAQ,KAAK;AACzC,gBAAY,KAAK,MAAM,CAAC,CAAC;AAAA,EAC7B;AACA,SAAO,YAAY,KAAK,IAAI;AAChC;AAaO,SAAS,aAAa,SAAS,SAAS;AAC3C,QAAM,SAAS,OAAO,YAAY,WAAW,WAAW,OAAO,IAAI;AACnE,MAAI,eAAe;AACnB,WAAS,eAAe;AACpB,UAAM,QAAQ,OAAO,cAAc;AACnC,QAAI,CAAC,OAAO;AACR,aAAO,QAAQ,SAAS;AAAA,IAC5B;AACA,YAAQ,SAAS,OAAO,SAAU,KAAK,MAAM;AACzC,UAAI,KAAK;AACL,eAAO,QAAQ,SAAS,GAAG;AAAA,MAC/B;AACA,YAAM,iBAAiB,WAAW,MAAM,OAAO,OAAO;AACtD,cAAQ,QAAQ,OAAO,gBAAgB,SAAUC,MAAK;AAClD,YAAIA,MAAK;AACL,iBAAO,QAAQ,SAASA,IAAG;AAAA,QAC/B;AACA,qBAAa;AAAA,MACjB,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AACA,eAAa;AACjB;;;AChQO,SAAS,aAAaC,kBAAiB;AAC1C,MAAI,MAAM,QAAQA,gBAAe,GAAG;AAEhC,WAAOA,iBAAgB,IAAI,WAAS,aAAa,KAAK,CAAC,EAAE,QAAQ;AAAA,EACrE;AACA,SAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAGA,gBAAe,GAAG,EAAE,aAAaA,iBAAgB,aAAa,WAAWA,iBAAgB,WAAW,aAAaA,iBAAgB,aAAa,WAAWA,iBAAgB,WAAW,OAAOA,iBAAgB,MAAM,IAAI,UAAQ;AAC5P,WAAO;AAAA,MACH,UAAU,KAAK;AAAA,MACf,UAAU,KAAK;AAAA,MACf,UAAU,KAAK;AAAA,MACf,UAAU,KAAK;AAAA,MACf,OAAO,KAAK,MAAM,IAAI,OAAK;AACvB,YAAI,EAAE,WAAW,GAAG,GAAG;AACnB,iBAAO,IAAI,EAAE,MAAM,CAAC,CAAC;AAAA,QACzB;AACA,YAAI,EAAE,WAAW,GAAG,GAAG;AACnB,iBAAO,IAAI,EAAE,MAAM,CAAC,CAAC;AAAA,QACzB;AACA,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ,CAAC,EAAE,CAAC;AACZ;;;ACrBO,SAAS,gBAAgB,aAAa,aAAa,QAAQ,QAAQ,WAAW,WAAW,SAAS;AACrG,MAAI;AACJ,MAAI,CAAC,SAAS;AACV,iBAAa,CAAC;AAAA,EAClB,WACS,OAAO,YAAY,YAAY;AACpC,iBAAa,EAAE,UAAU,QAAQ;AAAA,EACrC,OACK;AACD,iBAAa;AAAA,EACjB;AACA,MAAI,OAAO,WAAW,YAAY,aAAa;AAC3C,eAAW,UAAU;AAAA,EACzB;AAGA,QAAM,UAAU,WAAW;AAE3B,MAAI,WAAW,gBAAgB;AAC3B,UAAM,IAAI,MAAM,6FAA6F;AAAA,EACjH;AACA,MAAI,CAAC,WAAW,UAAU;AACtB,WAAO,uBAAuB,UAAU,QAAQ,QAAQ,UAAU,CAAC;AAAA,EACvE,OACK;AACD,UAAM,EAAE,SAAS,IAAI;AACrB,cAAU,QAAQ,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG,EAAE,UAAU,CAAC,SAAS;AACrF,YAAM,QAAQ,uBAAuB,IAAI;AAGzC,eAAS,KAAK;AAAA,IAClB,EAAE,CAAC,CAAC;AAAA,EACZ;AACA,WAAS,uBAAuB,MAAM;AAGlC,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,SAAK,KAAK,EAAE,OAAO,IAAI,OAAO,CAAC,EAAE,CAAC;AAClC,aAAS,aAAa,OAAO;AACzB,aAAO,MAAM,IAAI,SAAU,OAAO;AAAE,eAAO,MAAM;AAAA,MAAO,CAAC;AAAA,IAC7D;AACA,UAAM,QAAQ,CAAC;AACf,QAAI,gBAAgB,GAAG,gBAAgB,GAAG,WAAW,CAAC,GAAG,UAAU,GAAG,UAAU;AAChF,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,YAAM,UAAU,KAAK,CAAC,GAAG,QAAQ,QAAQ,SAAS,WAAW,QAAQ,KAAK;AAC1E,cAAQ,QAAQ;AAChB,UAAI,QAAQ,SAAS,QAAQ,SAAS;AAElC,YAAI,CAAC,eAAe;AAChB,gBAAM,OAAO,KAAK,IAAI,CAAC;AACvB,0BAAgB;AAChB,0BAAgB;AAChB,cAAI,MAAM;AACN,uBAAW,UAAU,IAAI,aAAa,KAAK,MAAM,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;AACrE,6BAAiB,SAAS;AAC1B,6BAAiB,SAAS;AAAA,UAC9B;AAAA,QACJ;AAEA,mBAAW,QAAQ,OAAO;AACtB,mBAAS,MAAM,QAAQ,QAAQ,MAAM,OAAO,IAAI;AAAA,QACpD;AAEA,YAAI,QAAQ,OAAO;AACf,qBAAW,MAAM;AAAA,QACrB,OACK;AACD,qBAAW,MAAM;AAAA,QACrB;AAAA,MACJ,OACK;AAED,YAAI,eAAe;AAEf,cAAI,MAAM,UAAU,UAAU,KAAK,IAAI,KAAK,SAAS,GAAG;AAEpD,uBAAW,QAAQ,aAAa,KAAK,GAAG;AACpC,uBAAS,KAAK,IAAI;AAAA,YACtB;AAAA,UACJ,OACK;AAED,kBAAM,cAAc,KAAK,IAAI,MAAM,QAAQ,OAAO;AAClD,uBAAW,QAAQ,aAAa,MAAM,MAAM,GAAG,WAAW,CAAC,GAAG;AAC1D,uBAAS,KAAK,IAAI;AAAA,YACtB;AACA,kBAAM,OAAO;AAAA,cACT,UAAU;AAAA,cACV,UAAW,UAAU,gBAAgB;AAAA,cACrC,UAAU;AAAA,cACV,UAAW,UAAU,gBAAgB;AAAA,cACrC,OAAO;AAAA,YACX;AACA,kBAAM,KAAK,IAAI;AACf,4BAAgB;AAChB,4BAAgB;AAChB,uBAAW,CAAC;AAAA,UAChB;AAAA,QACJ;AACA,mBAAW,MAAM;AACjB,mBAAW,MAAM;AAAA,MACrB;AAAA,IACJ;AAGA,eAAW,QAAQ,OAAO;AACtB,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,YAAI,KAAK,MAAM,CAAC,EAAE,SAAS,IAAI,GAAG;AAC9B,eAAK,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,EAAE,MAAM,GAAG,EAAE;AAAA,QAC7C,OACK;AACD,eAAK,MAAM,OAAO,IAAI,GAAG,GAAG,8BAA8B;AAC1D;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,MACH;AAAA,MAA0B;AAAA,MAC1B;AAAA,MAAsB;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ;AACJ;AAKO,SAAS,YAAY,OAAO;AAC/B,MAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,WAAO,MAAM,IAAI,WAAW,EAAE,KAAK,IAAI;AAAA,EAC3C;AACA,QAAM,MAAM,CAAC;AACb,MAAI,MAAM,eAAe,MAAM,aAAa;AACxC,QAAI,KAAK,YAAY,MAAM,WAAW;AAAA,EAC1C;AACA,MAAI,KAAK,qEAAqE;AAC9E,MAAI,KAAK,SAAS,MAAM,eAAe,OAAO,MAAM,cAAc,cAAc,KAAK,MAAO,MAAM,UAAU;AAC5G,MAAI,KAAK,SAAS,MAAM,eAAe,OAAO,MAAM,cAAc,cAAc,KAAK,MAAO,MAAM,UAAU;AAC5G,WAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,KAAK;AACzC,UAAM,OAAO,MAAM,MAAM,CAAC;AAI1B,QAAI,KAAK,aAAa,GAAG;AACrB,WAAK,YAAY;AAAA,IACrB;AACA,QAAI,KAAK,aAAa,GAAG;AACrB,WAAK,YAAY;AAAA,IACrB;AACA,QAAI,KAAK,SAAS,KAAK,WAAW,MAAM,KAAK,WACvC,OAAO,KAAK,WAAW,MAAM,KAAK,WAClC,KAAK;AACX,eAAW,QAAQ,KAAK,OAAO;AAC3B,UAAI,KAAK,IAAI;AAAA,IACjB;AAAA,EACJ;AACA,SAAO,IAAI,KAAK,IAAI,IAAI;AAC5B;AACO,SAAS,oBAAoB,aAAa,aAAa,QAAQ,QAAQ,WAAW,WAAW,SAAS;AACzG,MAAI,OAAO,YAAY,YAAY;AAC/B,cAAU,EAAE,UAAU,QAAQ;AAAA,EAClC;AACA,MAAI,EAAE,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,WAAW;AACvE,UAAM,WAAW,gBAAgB,aAAa,aAAa,QAAQ,QAAQ,WAAW,WAAW,OAAO;AACxG,QAAI,CAAC,UAAU;AACX;AAAA,IACJ;AACA,WAAO,YAAY,QAAQ;AAAA,EAC/B,OACK;AACD,UAAM,EAAE,SAAS,IAAI;AACrB,oBAAgB,aAAa,aAAa,QAAQ,QAAQ,WAAW,WAAW,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,UAAU,cAAY;AAC1I,UAAI,CAAC,UAAU;AACX,iBAAS,MAAS;AAAA,MACtB,OACK;AACD,iBAAS,YAAY,QAAQ,CAAC;AAAA,MAClC;AAAA,IACJ,EAAE,CAAC,CAAC;AAAA,EACZ;AACJ;AACO,SAAS,YAAY,UAAU,QAAQ,QAAQ,WAAW,WAAW,SAAS;AACjF,SAAO,oBAAoB,UAAU,UAAU,QAAQ,QAAQ,WAAW,WAAW,OAAO;AAChG;AAIA,SAAS,WAAW,MAAM;AACtB,QAAM,gBAAgB,KAAK,SAAS,IAAI;AACxC,QAAM,SAAS,KAAK,MAAM,IAAI,EAAE,IAAI,UAAQ,OAAO,IAAI;AACvD,MAAI,eAAe;AACf,WAAO,IAAI;AAAA,EACf,OACK;AACD,WAAO,KAAK,OAAO,IAAI,EAAE,MAAM,GAAG,EAAE,CAAC;AAAA,EACzC;AACA,SAAO;AACX;;;ACrMO,SAAS,oBAAoB,SAAS;AACzC,QAAM,MAAM,CAAC;AACb,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,aAAS,QAAQ,CAAC;AAClB,QAAI,OAAO,OAAO;AACd,kBAAY;AAAA,IAChB,WACS,OAAO,SAAS;AACrB,kBAAY;AAAA,IAChB,OACK;AACD,kBAAY;AAAA,IAChB;AACA,QAAI,KAAK,CAAC,WAAW,OAAO,KAAK,CAAC;AAAA,EACtC;AACA,SAAO;AACX;;;ACjBO,SAAS,oBAAoB,SAAS;AACzC,QAAM,MAAM,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,UAAM,SAAS,QAAQ,CAAC;AACxB,QAAI,OAAO,OAAO;AACd,UAAI,KAAK,OAAO;AAAA,IACpB,WACS,OAAO,SAAS;AACrB,UAAI,KAAK,OAAO;AAAA,IACpB;AACA,QAAI,KAAK,WAAW,OAAO,KAAK,CAAC;AACjC,QAAI,OAAO,OAAO;AACd,UAAI,KAAK,QAAQ;AAAA,IACrB,WACS,OAAO,SAAS;AACrB,UAAI,KAAK,QAAQ;AAAA,IACrB;AAAA,EACJ;AACA,SAAO,IAAI,KAAK,EAAE;AACtB;AACA,SAAS,WAAW,GAAG;AACnB,MAAI,IAAI;AACR,MAAI,EAAE,QAAQ,MAAM,OAAO;AAC3B,MAAI,EAAE,QAAQ,MAAM,MAAM;AAC1B,MAAI,EAAE,QAAQ,MAAM,MAAM;AAC1B,MAAI,EAAE,QAAQ,MAAM,QAAQ;AAC5B,SAAO;AACX;", "names": ["value", "key", "i", "err", "structuredPatch"]}