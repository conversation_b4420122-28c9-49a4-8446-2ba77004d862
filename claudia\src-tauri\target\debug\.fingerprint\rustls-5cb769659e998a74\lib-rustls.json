{"rustc": 16591470773350601817, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1677134433092527515, "path": 2082677791021378026, "deps": [[2883436298747778685, "pki_types", false, 1093944903609859800], [3722963349756955755, "once_cell", false, 9369741635030667748], [5491919304041016563, "ring", false, 14842450034411559943], [6528079939221783635, "zeroize", false, 4216946595622229801], [16400140949089969347, "build_script_build", false, 1163744958001177068], [17003143334332120809, "subtle", false, 1588678725831303909], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 17242844661350764586]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-5cb769659e998a74\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}