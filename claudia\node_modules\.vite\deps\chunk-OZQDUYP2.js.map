{"version": 3, "sources": ["../../refractor/lang/javascript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = javascript\njavascript.displayName = 'javascript'\njavascript.aliases = ['js']\nfunction javascript(Prism) {\n  Prism.languages.javascript = Prism.languages.extend('clike', {\n    'class-name': [\n      Prism.languages.clike['class-name'],\n      {\n        pattern:\n          /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$A-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\.(?:constructor|prototype))/,\n        lookbehind: true\n      }\n    ],\n    keyword: [\n      {\n        pattern: /((?:^|\\})\\s*)catch\\b/,\n        lookbehind: true\n      },\n      {\n        pattern:\n          /(^|[^.]|\\.\\.\\.\\s*)\\b(?:as|assert(?=\\s*\\{)|async(?=\\s*(?:function\\b|\\(|[$\\w\\xA0-\\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\\s*(?:\\{|$))|for|from(?=\\s*(?:['\"]|$))|function|(?:get|set)(?=\\s*(?:[#\\[$\\w\\xA0-\\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\\b/,\n        lookbehind: true\n      }\n    ],\n    // Allow for all non-ASCII characters (See http://stackoverflow.com/a/2008444)\n    function:\n      /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*(?:\\.\\s*(?:apply|bind|call)\\s*)?\\()/,\n    number: {\n      pattern: RegExp(\n        /(^|[^\\w$])/.source +\n          '(?:' + // constant\n          (/NaN|Infinity/.source +\n            '|' + // binary integer\n            /0[bB][01]+(?:_[01]+)*n?/.source +\n            '|' + // octal integer\n            /0[oO][0-7]+(?:_[0-7]+)*n?/.source +\n            '|' + // hexadecimal integer\n            /0[xX][\\dA-Fa-f]+(?:_[\\dA-Fa-f]+)*n?/.source +\n            '|' + // decimal bigint\n            /\\d+(?:_\\d+)*n/.source +\n            '|' + // decimal number (integer or float) but no bigint\n            /(?:\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\.\\d+(?:_\\d+)*)(?:[Ee][+-]?\\d+(?:_\\d+)*)?/\n              .source) +\n          ')' +\n          /(?![\\w$])/.source\n      ),\n      lookbehind: true\n    },\n    operator:\n      /--|\\+\\+|\\*\\*=?|=>|&&=?|\\|\\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\\.{3}|\\?\\?=?|\\?\\.?|[~:]/\n  })\n  Prism.languages.javascript['class-name'][0].pattern =\n    /(\\b(?:class|extends|implements|instanceof|interface|new)\\s+)[\\w.\\\\]+/\n  Prism.languages.insertBefore('javascript', 'keyword', {\n    regex: {\n      // eslint-disable-next-line regexp/no-dupe-characters-character-class\n      pattern:\n        /((?:^|[^$\\w\\xA0-\\uFFFF.\"'\\])\\s]|\\b(?:return|yield))\\s*)\\/(?:\\[(?:[^\\]\\\\\\r\\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}(?=(?:\\s|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*(?:$|[\\r\\n,.;:})\\]]|\\/\\/))/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        'regex-source': {\n          pattern: /^(\\/)[\\s\\S]+(?=\\/[a-z]*$)/,\n          lookbehind: true,\n          alias: 'language-regex',\n          inside: Prism.languages.regex\n        },\n        'regex-delimiter': /^\\/|\\/$/,\n        'regex-flags': /^[a-z]+$/\n      }\n    },\n    // This must be declared before keyword because we use \"function\" inside the look-forward\n    'function-variable': {\n      pattern:\n        /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*[=:]\\s*(?:async\\s*)?(?:\\bfunction\\b|(?:\\((?:[^()]|\\([^()]*\\))*\\)|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/,\n      alias: 'function'\n    },\n    parameter: [\n      {\n        pattern:\n          /(function(?:\\s+(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)?\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\))/,\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      },\n      {\n        pattern:\n          /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=>)/i,\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      },\n      {\n        pattern:\n          /(\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*=>)/,\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      },\n      {\n        pattern:\n          /((?:\\b|\\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\\w\\xA0-\\uFFFF]))(?:(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*)\\(\\s*|\\]\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*\\{)/,\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      }\n    ],\n    constant: /\\b[A-Z](?:[A-Z_]|\\dx?)*\\b/\n  })\n  Prism.languages.insertBefore('javascript', 'string', {\n    hashbang: {\n      pattern: /^#!.*/,\n      greedy: true,\n      alias: 'comment'\n    },\n    'template-string': {\n      pattern:\n        /`(?:\\\\[\\s\\S]|\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}|(?!\\$\\{)[^\\\\`])*`/,\n      greedy: true,\n      inside: {\n        'template-punctuation': {\n          pattern: /^`|`$/,\n          alias: 'string'\n        },\n        interpolation: {\n          pattern:\n            /((?:^|[^\\\\])(?:\\\\{2})*)\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,\n          lookbehind: true,\n          inside: {\n            'interpolation-punctuation': {\n              pattern: /^\\$\\{|\\}$/,\n              alias: 'punctuation'\n            },\n            rest: Prism.languages.javascript\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    },\n    'string-property': {\n      pattern:\n        /((?:^|[,{])[ \\t]*)([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\2)[^\\\\\\r\\n])*\\2(?=\\s*:)/m,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property'\n    }\n  })\n  Prism.languages.insertBefore('javascript', 'operator', {\n    'literal-property': {\n      pattern:\n        /((?:^|[,{])[ \\t]*)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*:)/m,\n      lookbehind: true,\n      alias: 'property'\n    }\n  })\n  if (Prism.languages.markup) {\n    Prism.languages.markup.tag.addInlined('script', 'javascript') // add attribute support for all DOM events.\n    // https://developer.mozilla.org/en-US/docs/Web/Events#Standard_events\n    Prism.languages.markup.tag.addAttribute(\n      /on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/\n        .source,\n      'javascript'\n    )\n  }\n  Prism.languages.js = Prism.languages.javascript\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,eAAW,cAAc;AACzB,eAAW,UAAU,CAAC,IAAI;AAC1B,aAAS,WAAW,OAAO;AACzB,YAAM,UAAU,aAAa,MAAM,UAAU,OAAO,SAAS;AAAA,QAC3D,cAAc;AAAA,UACZ,MAAM,UAAU,MAAM,YAAY;AAAA,UAClC;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,QACF;AAAA;AAAA,QAEA,UACE;AAAA,QACF,QAAQ;AAAA,UACN,SAAS;AAAA,YACP,aAAa,SACX;AAAA,aACC,eAAe,SACd;AAAA,YACA,0BAA0B,SAC1B;AAAA,YACA,4BAA4B,SAC5B;AAAA,YACA,sCAAsC,SACtC;AAAA,YACA,gBAAgB,SAChB;AAAA,YACA,oFACG,UACL,MACA,YAAY;AAAA,UAChB;AAAA,UACA,YAAY;AAAA,QACd;AAAA,QACA,UACE;AAAA,MACJ,CAAC;AACD,YAAM,UAAU,WAAW,YAAY,EAAE,CAAC,EAAE,UAC1C;AACF,YAAM,UAAU,aAAa,cAAc,WAAW;AAAA,QACpD,OAAO;AAAA;AAAA,UAEL,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,gBAAgB;AAAA,cACd,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,cACP,QAAQ,MAAM,UAAU;AAAA,YAC1B;AAAA,YACA,mBAAmB;AAAA,YACnB,eAAe;AAAA,UACjB;AAAA,QACF;AAAA;AAAA,QAEA,qBAAqB;AAAA,UACnB,SACE;AAAA,UACF,OAAO;AAAA,QACT;AAAA,QACA,WAAW;AAAA,UACT;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ,MAAM,UAAU;AAAA,UAC1B;AAAA,UACA;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ,MAAM,UAAU;AAAA,UAC1B;AAAA,UACA;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ,MAAM,UAAU;AAAA,UAC1B;AAAA,UACA;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ,MAAM,UAAU;AAAA,UAC1B;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,CAAC;AACD,YAAM,UAAU,aAAa,cAAc,UAAU;AAAA,QACnD,UAAU;AAAA,UACR,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,SACE;AAAA,UACF,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,wBAAwB;AAAA,cACtB,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,YACA,eAAe;AAAA,cACb,SACE;AAAA,cACF,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,6BAA6B;AAAA,kBAC3B,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA,gBACA,MAAM,MAAM,UAAU;AAAA,cACxB;AAAA,YACF;AAAA,YACA,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,QACA,mBAAmB;AAAA,UACjB,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AAAA,MACF,CAAC;AACD,YAAM,UAAU,aAAa,cAAc,YAAY;AAAA,QACrD,oBAAoB;AAAA,UAClB,SACE;AAAA,UACF,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,MACF,CAAC;AACD,UAAI,MAAM,UAAU,QAAQ;AAC1B,cAAM,UAAU,OAAO,IAAI,WAAW,UAAU,YAAY;AAE5D,cAAM,UAAU,OAAO,IAAI;AAAA,UACzB,yNACG;AAAA,UACH;AAAA,QACF;AAAA,MACF;AACA,YAAM,UAAU,KAAK,MAAM,UAAU;AAAA,IACvC;AAAA;AAAA;", "names": []}