{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 15657897354478470176, "path": 15070104006516356483, "deps": [[500211409582349667, "shared_child", false, 15064033591102091799], [1582828171158827377, "build_script_build", false, 8483448232152489050], [5986029879202738730, "log", false, 785926723577402214], [9451456094439810778, "regex", false, 5755855846040275993], [9538054652646069845, "tokio", false, 14483482792075840282], [9689903380558560274, "serde", false, 11564652921496065999], [10755362358622467486, "tauri", false, 16124978675318053989], [10806645703491011684, "thiserror", false, 5650462710600407025], [11337703028400419576, "os_pipe", false, 4195857040799633858], [14564311161534545801, "encoding_rs", false, 12911914667759783197], [15367738274754116744, "serde_json", false, 6550851001671471717], [16192041687293812804, "open", false, 10088745116254646586]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-shell-333ac936b93896e1\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}