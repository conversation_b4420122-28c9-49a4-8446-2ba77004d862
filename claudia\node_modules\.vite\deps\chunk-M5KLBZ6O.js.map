{"version": 3, "sources": ["../../refractor/lang/gn.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = gn\ngn.displayName = 'gn'\ngn.aliases = ['gni']\nfunction gn(Prism) {\n  // https://gn.googlesource.com/gn/+/refs/heads/main/docs/reference.md#grammar\n  Prism.languages.gn = {\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    'string-literal': {\n      pattern: /(^|[^\\\\\"])\"(?:[^\\r\\n\"\\\\]|\\\\.)*\"/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern:\n            /((?:^|[^\\\\])(?:\\\\{2})*)\\$(?:\\{[\\s\\S]*?\\}|[a-zA-Z_]\\w*|0x[a-fA-F0-9]{2})/,\n          lookbehind: true,\n          inside: {\n            number: /^\\$0x[\\s\\S]{2}$/,\n            variable: /^\\$\\w+$/,\n            'interpolation-punctuation': {\n              pattern: /^\\$\\{|\\}$/,\n              alias: 'punctuation'\n            },\n            expression: {\n              pattern: /[\\s\\S]+/,\n              inside: null // see below\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    },\n    keyword: /\\b(?:else|if)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    'builtin-function': {\n      // a few functions get special highlighting to improve readability\n      pattern:\n        /\\b(?:assert|defined|foreach|import|pool|print|template|tool|toolchain)(?=\\s*\\()/i,\n      alias: 'keyword'\n    },\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    constant:\n      /\\b(?:current_cpu|current_os|current_toolchain|default_toolchain|host_cpu|host_os|root_build_dir|root_gen_dir|root_out_dir|target_cpu|target_gen_dir|target_os|target_out_dir)\\b/,\n    number: /-?\\b\\d+\\b/,\n    operator: /[-+!=<>]=?|&&|\\|\\|/,\n    punctuation: /[(){}[\\],.]/\n  }\n  Prism.languages.gn['string-literal'].inside['interpolation'].inside[\n    'expression'\n  ].inside = Prism.languages.gn\n  Prism.languages.gni = Prism.languages.gn\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,OAAG,cAAc;AACjB,OAAG,UAAU,CAAC,KAAK;AACnB,aAAS,GAAG,OAAO;AAEjB,YAAM,UAAU,KAAK;AAAA,QACnB,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,kBAAkB;AAAA,UAChB,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,eAAe;AAAA,cACb,SACE;AAAA,cACF,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,QAAQ;AAAA,gBACR,UAAU;AAAA,gBACV,6BAA6B;AAAA,kBAC3B,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA,gBACA,YAAY;AAAA,kBACV,SAAS;AAAA,kBACT,QAAQ;AAAA;AAAA,gBACV;AAAA,cACF;AAAA,YACF;AAAA,YACA,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,QACA,SAAS;AAAA,QACT,SAAS;AAAA,QACT,oBAAoB;AAAA;AAAA,UAElB,SACE;AAAA,UACF,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,QACV,UACE;AAAA,QACF,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AACA,YAAM,UAAU,GAAG,gBAAgB,EAAE,OAAO,eAAe,EAAE,OAC3D,YACF,EAAE,SAAS,MAAM,UAAU;AAC3B,YAAM,UAAU,MAAM,MAAM,UAAU;AAAA,IACxC;AAAA;AAAA;", "names": []}