{"rustc": 16591470773350601817, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 15657897354478470176, "path": 18117763410692053574, "deps": [[784494742817713399, "tower_service", false, 9825182280476596558], [1906322745568073236, "pin_project_lite", false, 12513370387149380344], [4121350475192885151, "iri_string", false, 4264459047755067947], [5695049318159433696, "tower", false, 569291546811942496], [7712452662827335977, "tower_layer", false, 4740052445820352768], [7896293946984509699, "bitflags", false, 15445298471420156998], [9010263965687315507, "http", false, 18131092521246687413], [10629569228670356391, "futures_util", false, 8612617707734785946], [14084095096285906100, "http_body", false, 13023911694743650655], [16066129441945555748, "bytes", false, 4443973527350322883]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-3df5511e46d5f5d2\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}