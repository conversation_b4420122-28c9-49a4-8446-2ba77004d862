{"rustc": 16591470773350601817, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1988539120246850232, "path": 2082677791021378026, "deps": [[2883436298747778685, "pki_types", false, 10230618246725475637], [3722963349756955755, "once_cell", false, 3343094852291253540], [5491919304041016563, "ring", false, 9769731288132654703], [6528079939221783635, "zeroize", false, 5938089318775180734], [16400140949089969347, "build_script_build", false, 2455942805319384064], [17003143334332120809, "subtle", false, 16744984691977535189], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 7956823906049898254]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rustls-8b733f98e6adc7e3\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}