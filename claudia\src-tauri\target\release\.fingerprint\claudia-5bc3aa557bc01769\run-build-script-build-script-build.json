{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9805105373369294601, "build_script_build", false, 5247278309889918451], [10755362358622467486, "build_script_build", false, 1045948375113365585], [13919194856117907555, "build_script_build", false, 14319516582914552443], [3834743577069889284, "build_script_build", false, 10837461579352329343], [13890802266741835355, "build_script_build", false, 13756839327475531156], [246920333930397414, "build_script_build", false, 13498568240423241428], [15441187897486245138, "build_script_build", false, 9035713433476499181], [7849236192756901113, "build_script_build", false, 6012618645808553263], [17962022290347926134, "build_script_build", false, 10756742744892153135], [1582828171158827377, "build_script_build", false, 3378115986429718992], [18440762029541581206, "build_script_build", false, 8406149273522000934]], "local": [{"RerunIfChanged": {"output": "release\\build\\claudia-5bc3aa557bc01769\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}