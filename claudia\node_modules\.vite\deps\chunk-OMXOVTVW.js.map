{"version": 3, "sources": ["../../refractor/lang/ruby.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = ruby\nruby.displayName = 'ruby'\nruby.aliases = ['rb']\nfunction ruby(Prism) {\n  /**\n   * Original by <PERSON>\n   *\n   * Adds the following new token classes:\n   *     constant, builtin, variable, symbol, regex\n   */\n  ;(function (Prism) {\n    Prism.languages.ruby = Prism.languages.extend('clike', {\n      comment: {\n        pattern: /#.*|^=begin\\s[\\s\\S]*?^=end/m,\n        greedy: true\n      },\n      'class-name': {\n        pattern:\n          /(\\b(?:class|module)\\s+|\\bcatch\\s+\\()[\\w.\\\\]+|\\b[A-Z_]\\w*(?=\\s*\\.\\s*new\\b)/,\n        lookbehind: true,\n        inside: {\n          punctuation: /[.\\\\]/\n        }\n      },\n      keyword:\n        /\\b(?:BEGIN|END|alias|and|begin|break|case|class|def|define_method|defined|do|each|else|elsif|end|ensure|extend|for|if|in|include|module|new|next|nil|not|or|prepend|private|protected|public|raise|redo|require|rescue|retry|return|self|super|then|throw|undef|unless|until|when|while|yield)\\b/,\n      operator:\n        /\\.{2,3}|&\\.|===|<?=>|[!=]?~|(?:&&|\\|\\||<<|>>|\\*\\*|[+\\-*/%<>!^&|=])=?|[?:]/,\n      punctuation: /[(){}[\\].,;]/\n    })\n    Prism.languages.insertBefore('ruby', 'operator', {\n      'double-colon': {\n        pattern: /::/,\n        alias: 'punctuation'\n      }\n    })\n    var interpolation = {\n      pattern: /((?:^|[^\\\\])(?:\\\\{2})*)#\\{(?:[^{}]|\\{[^{}]*\\})*\\}/,\n      lookbehind: true,\n      inside: {\n        content: {\n          pattern: /^(#\\{)[\\s\\S]+(?=\\}$)/,\n          lookbehind: true,\n          inside: Prism.languages.ruby\n        },\n        delimiter: {\n          pattern: /^#\\{|\\}$/,\n          alias: 'punctuation'\n        }\n      }\n    }\n    delete Prism.languages.ruby.function\n    var percentExpression =\n      '(?:' +\n      [\n        /([^a-zA-Z0-9\\s{(\\[<=])(?:(?!\\1)[^\\\\]|\\\\[\\s\\S])*\\1/.source,\n        /\\((?:[^()\\\\]|\\\\[\\s\\S]|\\((?:[^()\\\\]|\\\\[\\s\\S])*\\))*\\)/.source,\n        /\\{(?:[^{}\\\\]|\\\\[\\s\\S]|\\{(?:[^{}\\\\]|\\\\[\\s\\S])*\\})*\\}/.source,\n        /\\[(?:[^\\[\\]\\\\]|\\\\[\\s\\S]|\\[(?:[^\\[\\]\\\\]|\\\\[\\s\\S])*\\])*\\]/.source,\n        /<(?:[^<>\\\\]|\\\\[\\s\\S]|<(?:[^<>\\\\]|\\\\[\\s\\S])*>)*>/.source\n      ].join('|') +\n      ')'\n    var symbolName =\n      /(?:\"(?:\\\\.|[^\"\\\\\\r\\n])*\"|(?:\\b[a-zA-Z_]\\w*|[^\\s\\0-\\x7F]+)[?!]?|\\$.)/\n        .source\n    Prism.languages.insertBefore('ruby', 'keyword', {\n      'regex-literal': [\n        {\n          pattern: RegExp(\n            /%r/.source + percentExpression + /[egimnosux]{0,6}/.source\n          ),\n          greedy: true,\n          inside: {\n            interpolation: interpolation,\n            regex: /[\\s\\S]+/\n          }\n        },\n        {\n          pattern:\n            /(^|[^/])\\/(?!\\/)(?:\\[[^\\r\\n\\]]+\\]|\\\\.|[^[/\\\\\\r\\n])+\\/[egimnosux]{0,6}(?=\\s*(?:$|[\\r\\n,.;})#]))/,\n          lookbehind: true,\n          greedy: true,\n          inside: {\n            interpolation: interpolation,\n            regex: /[\\s\\S]+/\n          }\n        }\n      ],\n      variable: /[@$]+[a-zA-Z_]\\w*(?:[?!]|\\b)/,\n      symbol: [\n        {\n          pattern: RegExp(/(^|[^:]):/.source + symbolName),\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          pattern: RegExp(\n            /([\\r\\n{(,][ \\t]*)/.source + symbolName + /(?=:(?!:))/.source\n          ),\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      'method-definition': {\n        pattern: /(\\bdef\\s+)\\w+(?:\\s*\\.\\s*\\w+)?/,\n        lookbehind: true,\n        inside: {\n          function: /\\b\\w+$/,\n          keyword: /^self\\b/,\n          'class-name': /^\\w+/,\n          punctuation: /\\./\n        }\n      }\n    })\n    Prism.languages.insertBefore('ruby', 'string', {\n      'string-literal': [\n        {\n          pattern: RegExp(/%[qQiIwWs]?/.source + percentExpression),\n          greedy: true,\n          inside: {\n            interpolation: interpolation,\n            string: /[\\s\\S]+/\n          }\n        },\n        {\n          pattern:\n            /(\"|')(?:#\\{[^}]+\\}|#(?!\\{)|\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\#\\r\\n])*\\1/,\n          greedy: true,\n          inside: {\n            interpolation: interpolation,\n            string: /[\\s\\S]+/\n          }\n        },\n        {\n          pattern: /<<[-~]?([a-z_]\\w*)[\\r\\n](?:.*[\\r\\n])*?[\\t ]*\\1/i,\n          alias: 'heredoc-string',\n          greedy: true,\n          inside: {\n            delimiter: {\n              pattern: /^<<[-~]?[a-z_]\\w*|\\b[a-z_]\\w*$/i,\n              inside: {\n                symbol: /\\b\\w+/,\n                punctuation: /^<<[-~]?/\n              }\n            },\n            interpolation: interpolation,\n            string: /[\\s\\S]+/\n          }\n        },\n        {\n          pattern: /<<[-~]?'([a-z_]\\w*)'[\\r\\n](?:.*[\\r\\n])*?[\\t ]*\\1/i,\n          alias: 'heredoc-string',\n          greedy: true,\n          inside: {\n            delimiter: {\n              pattern: /^<<[-~]?'[a-z_]\\w*'|\\b[a-z_]\\w*$/i,\n              inside: {\n                symbol: /\\b\\w+/,\n                punctuation: /^<<[-~]?'|'$/\n              }\n            },\n            string: /[\\s\\S]+/\n          }\n        }\n      ],\n      'command-literal': [\n        {\n          pattern: RegExp(/%x/.source + percentExpression),\n          greedy: true,\n          inside: {\n            interpolation: interpolation,\n            command: {\n              pattern: /[\\s\\S]+/,\n              alias: 'string'\n            }\n          }\n        },\n        {\n          pattern: /`(?:#\\{[^}]+\\}|#(?!\\{)|\\\\(?:\\r\\n|[\\s\\S])|[^\\\\`#\\r\\n])*`/,\n          greedy: true,\n          inside: {\n            interpolation: interpolation,\n            command: {\n              pattern: /[\\s\\S]+/,\n              alias: 'string'\n            }\n          }\n        }\n      ]\n    })\n    delete Prism.languages.ruby.string\n    Prism.languages.insertBefore('ruby', 'number', {\n      builtin:\n        /\\b(?:Array|Bignum|Binding|Class|Continuation|Dir|Exception|FalseClass|File|Fixnum|Float|Hash|IO|Integer|MatchData|Method|Module|NilClass|Numeric|Object|Proc|Range|Regexp|Stat|String|Struct|Symbol|TMS|Thread|ThreadGroup|Time|TrueClass)\\b/,\n      constant: /\\b[A-Z][A-Z0-9_]*(?:[?!]|\\b)/\n    })\n    Prism.languages.rb = Prism.languages.ruby\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC,IAAI;AACpB,aAAS,KAAK,OAAO;AAOnB;AAAC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,OAAOA,OAAM,UAAU,OAAO,SAAS;AAAA,UACrD,SAAS;AAAA,YACP,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,cAAc;AAAA,YACZ,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,SACE;AAAA,UACF,UACE;AAAA,UACF,aAAa;AAAA,QACf,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,QAAQ,YAAY;AAAA,UAC/C,gBAAgB;AAAA,YACd,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACD,YAAI,gBAAgB;AAAA,UAClB,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,SAAS;AAAA,cACP,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQA,OAAM,UAAU;AAAA,YAC1B;AAAA,YACA,WAAW;AAAA,cACT,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AACA,eAAOA,OAAM,UAAU,KAAK;AAC5B,YAAI,oBACF,QACA;AAAA,UACE,oDAAoD;AAAA,UACpD,sDAAsD;AAAA,UACtD,sDAAsD;AAAA,UACtD,0DAA0D;AAAA,UAC1D,kDAAkD;AAAA,QACpD,EAAE,KAAK,GAAG,IACV;AACF,YAAI,aACF,sEACG;AACL,QAAAA,OAAM,UAAU,aAAa,QAAQ,WAAW;AAAA,UAC9C,iBAAiB;AAAA,YACf;AAAA,cACE,SAAS;AAAA,gBACP,KAAK,SAAS,oBAAoB,mBAAmB;AAAA,cACvD;AAAA,cACA,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN;AAAA,gBACA,OAAO;AAAA,cACT;AAAA,YACF;AAAA,YACA;AAAA,cACE,SACE;AAAA,cACF,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN;AAAA,gBACA,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA,UAAU;AAAA,UACV,QAAQ;AAAA,YACN;AAAA,cACE,SAAS,OAAO,YAAY,SAAS,UAAU;AAAA,cAC/C,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA;AAAA,cACE,SAAS;AAAA,gBACP,oBAAoB,SAAS,aAAa,aAAa;AAAA,cACzD;AAAA,cACA,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,UACA,qBAAqB;AAAA,YACnB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,UAAU;AAAA,cACV,SAAS;AAAA,cACT,cAAc;AAAA,cACd,aAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,QAAQ,UAAU;AAAA,UAC7C,kBAAkB;AAAA,YAChB;AAAA,cACE,SAAS,OAAO,cAAc,SAAS,iBAAiB;AAAA,cACxD,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN;AAAA,gBACA,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,YACA;AAAA,cACE,SACE;AAAA,cACF,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN;AAAA,gBACA,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,WAAW;AAAA,kBACT,SAAS;AAAA,kBACT,QAAQ;AAAA,oBACN,QAAQ;AAAA,oBACR,aAAa;AAAA,kBACf;AAAA,gBACF;AAAA,gBACA;AAAA,gBACA,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,WAAW;AAAA,kBACT,SAAS;AAAA,kBACT,QAAQ;AAAA,oBACN,QAAQ;AAAA,oBACR,aAAa;AAAA,kBACf;AAAA,gBACF;AAAA,gBACA,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,UACF;AAAA,UACA,mBAAmB;AAAA,YACjB;AAAA,cACE,SAAS,OAAO,KAAK,SAAS,iBAAiB;AAAA,cAC/C,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN;AAAA,gBACA,SAAS;AAAA,kBACP,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN;AAAA,gBACA,SAAS;AAAA,kBACP,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AACD,eAAOA,OAAM,UAAU,KAAK;AAC5B,QAAAA,OAAM,UAAU,aAAa,QAAQ,UAAU;AAAA,UAC7C,SACE;AAAA,UACF,UAAU;AAAA,QACZ,CAAC;AACD,QAAAA,OAAM,UAAU,KAAKA,OAAM,UAAU;AAAA,MACvC,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}