{"rustc": 16591470773350601817, "features": "[\"http1\", \"http2\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 15657897354478470176, "path": 2024714070379998524, "deps": [[778154619793643451, "hyper_util", false, 5718778137013015164], [784494742817713399, "tower_service", false, 9825182280476596558], [2883436298747778685, "pki_types", false, 1093944903609859800], [5907992341687085091, "webpki_roots", false, 12130717419585748600], [9010263965687315507, "http", false, 18131092521246687413], [9538054652646069845, "tokio", false, 14483482792075840282], [11895591994124935963, "tokio_rustls", false, 5145093823749184836], [11957360342995674422, "hyper", false, 14851675900839142679], [16400140949089969347, "rustls", false, 1990977607492622149]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hyper-rustls-186df0832fd1c7e0\\dep-lib-hyper_rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}