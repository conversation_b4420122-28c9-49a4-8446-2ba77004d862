{"rustc": 16591470773350601817, "features": "[\"bundled\", \"modern_sqlite\"]", "declared_features": "[\"array\", \"backup\", \"blob\", \"buildtime_bindgen\", \"bundled\", \"bundled-full\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"chrono\", \"collation\", \"column_decltype\", \"csv\", \"csvtab\", \"extra_check\", \"functions\", \"hooks\", \"i128_blob\", \"in_gecko\", \"limits\", \"load_extension\", \"loadable_extension\", \"modern-full\", \"modern_sqlite\", \"preupdate_hook\", \"release_memory\", \"rusqlite-macros\", \"serde_json\", \"serialize\", \"series\", \"session\", \"sqlcipher\", \"time\", \"trace\", \"unlock_notify\", \"url\", \"uuid\", \"vtab\", \"wasm32-wasi-vfs\", \"window\", \"with-asan\"]", "target": 10662205063260755052, "profile": 2040997289075261528, "path": 4671569629175566801, "deps": [[3056352129074654578, "hashlink", false, 7885024561007377959], [3666196340704888985, "smallvec", false, 6791113449381042669], [5510864063823219921, "fallible_streaming_iterator", false, 8000383669301186841], [7896293946984509699, "bitflags", false, 2195707029368540986], [12860549049674006569, "fallible_iterator", false, 2732291880577853755], [16675652872862304210, "libsqlite3_sys", false, 16464441992885567883]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rusqlite-89d6bd90899ada46\\dep-lib-rusqlite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}