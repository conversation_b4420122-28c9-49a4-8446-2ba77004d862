C:\Users\<USER>\Desktop\claude\claudia\src-tauri\target\debug\deps\libclaudia_lib.rlib: src\lib.rs src\checkpoint\mod.rs src\checkpoint\manager.rs src\checkpoint\state.rs src\checkpoint\storage.rs src\claude_binary.rs src\commands\mod.rs src\commands\agents.rs src\commands\claude.rs src\commands\mcp.rs src\commands\usage.rs src\commands\storage.rs src\commands\slash_commands.rs src\process\mod.rs src\process\registry.rs C:\Users\<USER>\Desktop\claude\claudia\src-tauri\target\debug\build\claudia-05723bb55f77a87c\out/aa9519d2a669e2476b6df1e4095ad7f8cb7d07fef7edf8869c66edbc7acbee93

C:\Users\<USER>\Desktop\claude\claudia\src-tauri\target\debug\deps\claudia_lib.dll: src\lib.rs src\checkpoint\mod.rs src\checkpoint\manager.rs src\checkpoint\state.rs src\checkpoint\storage.rs src\claude_binary.rs src\commands\mod.rs src\commands\agents.rs src\commands\claude.rs src\commands\mcp.rs src\commands\usage.rs src\commands\storage.rs src\commands\slash_commands.rs src\process\mod.rs src\process\registry.rs C:\Users\<USER>\Desktop\claude\claudia\src-tauri\target\debug\build\claudia-05723bb55f77a87c\out/aa9519d2a669e2476b6df1e4095ad7f8cb7d07fef7edf8869c66edbc7acbee93

C:\Users\<USER>\Desktop\claude\claudia\src-tauri\target\debug\deps\claudia_lib.lib: src\lib.rs src\checkpoint\mod.rs src\checkpoint\manager.rs src\checkpoint\state.rs src\checkpoint\storage.rs src\claude_binary.rs src\commands\mod.rs src\commands\agents.rs src\commands\claude.rs src\commands\mcp.rs src\commands\usage.rs src\commands\storage.rs src\commands\slash_commands.rs src\process\mod.rs src\process\registry.rs C:\Users\<USER>\Desktop\claude\claudia\src-tauri\target\debug\build\claudia-05723bb55f77a87c\out/aa9519d2a669e2476b6df1e4095ad7f8cb7d07fef7edf8869c66edbc7acbee93

C:\Users\<USER>\Desktop\claude\claudia\src-tauri\target\debug\deps\claudia_lib.d: src\lib.rs src\checkpoint\mod.rs src\checkpoint\manager.rs src\checkpoint\state.rs src\checkpoint\storage.rs src\claude_binary.rs src\commands\mod.rs src\commands\agents.rs src\commands\claude.rs src\commands\mcp.rs src\commands\usage.rs src\commands\storage.rs src\commands\slash_commands.rs src\process\mod.rs src\process\registry.rs C:\Users\<USER>\Desktop\claude\claudia\src-tauri\target\debug\build\claudia-05723bb55f77a87c\out/aa9519d2a669e2476b6df1e4095ad7f8cb7d07fef7edf8869c66edbc7acbee93

src\lib.rs:
src\checkpoint\mod.rs:
src\checkpoint\manager.rs:
src\checkpoint\state.rs:
src\checkpoint\storage.rs:
src\claude_binary.rs:
src\commands\mod.rs:
src\commands\agents.rs:
src\commands\claude.rs:
src\commands\mcp.rs:
src\commands\usage.rs:
src\commands\storage.rs:
src\commands\slash_commands.rs:
src\process\mod.rs:
src\process\registry.rs:
C:\Users\<USER>\Desktop\claude\claudia\src-tauri\target\debug\build\claudia-05723bb55f77a87c\out/aa9519d2a669e2476b6df1e4095ad7f8cb7d07fef7edf8869c66edbc7acbee93:

# env-dep:CARGO_MANIFEST_DIR=C:\\Users\\<USER>\\Desktop\\claude\\claudia\\src-tauri
# env-dep:CARGO_PKG_AUTHORS=mufeedvh:123vviekr
# env-dep:CARGO_PKG_DESCRIPTION=GUI app and Toolkit for Claude Code
# env-dep:CARGO_PKG_NAME=claudia
# env-dep:OUT_DIR=C:\\Users\\<USER>\\Desktop\\claude\\claudia\\src-tauri\\target\\debug\\build\\claudia-05723bb55f77a87c\\out
