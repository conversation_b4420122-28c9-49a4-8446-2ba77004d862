{"rustc": 16591470773350601817, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"macos-system-configuration\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"stream\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 7859547470675518382, "path": 897028823148492248, "deps": [[40386456601120721, "percent_encoding", false, 10394793211968261770], [418947936956741439, "h2", false, 3981198825122491124], [778154619793643451, "hyper_util", false, 9613792721699249436], [784494742817713399, "tower_service", false, 10076585654715999106], [1288403060204016458, "tokio_util", false, 2794249932618157996], [1788832197870803419, "hyper_rustls", false, 11453746919190888916], [1906322745568073236, "pin_project_lite", false, 12528054045957783443], [2054153378684941554, "tower_http", false, 4162700172367860822], [2517136641825875337, "sync_wrapper", false, 17845195047662823653], [2883436298747778685, "rustls_pki_types", false, 10230618246725475637], [3150220818285335163, "url", false, 14171641885309381843], [5695049318159433696, "tower", false, 12308486341347200113], [5907992341687085091, "webpki_roots", false, 10009452107301082057], [5986029879202738730, "log", false, 16506617143423921061], [7620660491849607393, "futures_core", false, 17347301603407158438], [8298091525883606470, "cookie_store", false, 17503898024052035362], [9010263965687315507, "http", false, 12213520909095809930], [9538054652646069845, "tokio", false, 9712805951141166731], [9689903380558560274, "serde", false, 7541355907771353291], [10229185211513642314, "mime", false, 2474990561902353895], [10629569228670356391, "futures_util", false, 16760756696038921274], [11895591994124935963, "tokio_rustls", false, 371741060251343189], [11957360342995674422, "hyper", false, 5312720896039631132], [12186126227181294540, "tokio_native_tls", false, 7499459574399964516], [13077212702700853852, "base64", false, 761710099355871549], [14084095096285906100, "http_body", false, 17137551870842748317], [14564311161534545801, "encoding_rs", false, 12684925332375899401], [15367738274754116744, "serde_json", false, 17470562606684216992], [16066129441945555748, "bytes", false, 18342550410450640939], [16400140949089969347, "rustls", false, 16554588080328024896], [16542808166767769916, "serde_urlencoded", false, 5698656094661755482], [16727543399706004146, "cookie_crate", false, 15313389710856208794], [16785601910559813697, "native_tls_crate", false, 4448422436808222188], [16900715236047033623, "http_body_util", false, 16897662491127977413], [18273243456331255970, "hyper_tls", false, 2184208985810989060]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-73fb0deb508f42e9\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}