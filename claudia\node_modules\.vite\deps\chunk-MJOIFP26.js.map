{"version": 3, "sources": ["../../highlight.js/lib/languages/zephir.js"], "sourcesContent": ["/*\n Language: Zephir\n Description: Zephir, an open source, high-level language designed to ease the creation and maintainability of extensions for PHP with a focus on type and memory safety.\n Author: <PERSON><PERSON> <<EMAIL>>\n Website: https://zephir-lang.com/en\n Audit: 2020\n */\n\n/** @type LanguageFn */\nfunction zephir(hljs) {\n  const STRING = {\n    className: 'string',\n    contains: [ hljs.BACKSLASH_ESCAPE ],\n    variants: [\n      hljs.inherit(hljs.APOS_STRING_MODE, {\n        illegal: null\n      }),\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        illegal: null\n      })\n    ]\n  };\n  const TITLE_MODE = hljs.UNDERSCORE_TITLE_MODE;\n  const NUMBER = {\n    variants: [\n      hljs.BINARY_NUMBER_MODE,\n      hljs.C_NUMBER_MODE\n    ]\n  };\n  const KEYWORDS =\n    // classes and objects\n    'namespace class interface use extends ' +\n    'function return ' +\n    'abstract final public protected private static deprecated ' +\n    // error handling\n    'throw try catch Exception ' +\n    // keyword-ish things their website does NOT seem to highlight (in their own snippets)\n    // 'typeof fetch in ' +\n    // operators/helpers\n    'echo empty isset instanceof unset ' +\n    // assignment/variables\n    'let var new const self ' +\n    // control\n    'require ' +\n    'if else elseif switch case default ' +\n    'do while loop for continue break ' +\n    'likely unlikely ' +\n    // magic constants\n    // https://github.com/phalcon/zephir/blob/master/Library/Expression/Constants.php\n    '__LINE__ __FILE__ __DIR__ __FUNCTION__ __CLASS__ __TRAIT__ __METHOD__ __NAMESPACE__ ' +\n    // types - https://docs.zephir-lang.com/0.12/en/types\n    'array boolean float double integer object resource string ' +\n    'char long unsigned bool int uint ulong uchar ' +\n    // built-ins\n    'true false null undefined';\n\n  return {\n    name: 'Zephir',\n    aliases: [ 'zep' ],\n    keywords: KEYWORDS,\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.COMMENT(\n        /\\/\\*/,\n        /\\*\\//,\n        {\n          contains: [\n            {\n              className: 'doctag',\n              begin: /@[A-Za-z]+/\n            }\n          ]\n        }\n      ),\n      {\n        className: 'string',\n        begin: /<<<['\"]?\\w+['\"]?$/,\n        end: /^\\w+;/,\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      {\n        // swallow composed identifiers to avoid parsing them as keywords\n        begin: /(::|->)+[a-zA-Z_\\x7f-\\xff][a-zA-Z0-9_\\x7f-\\xff]*/\n      },\n      {\n        className: 'function',\n        beginKeywords: 'function fn',\n        end: /[;{]/,\n        excludeEnd: true,\n        illegal: /\\$|\\[|%/,\n        contains: [\n          TITLE_MODE,\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            keywords: KEYWORDS,\n            contains: [\n              'self',\n              hljs.C_BLOCK_COMMENT_MODE,\n              STRING,\n              NUMBER\n            ]\n          }\n        ]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'class interface',\n        end: /\\{/,\n        excludeEnd: true,\n        illegal: /[:($\"]/,\n        contains: [\n          {\n            beginKeywords: 'extends implements'\n          },\n          TITLE_MODE\n        ]\n      },\n      {\n        beginKeywords: 'namespace',\n        end: /;/,\n        illegal: /[.']/,\n        contains: [ TITLE_MODE ]\n      },\n      {\n        beginKeywords: 'use',\n        end: /;/,\n        contains: [ TITLE_MODE ]\n      },\n      {\n        begin: /=>/ // No markup, just a relevance booster\n      },\n      STRING,\n      NUMBER\n    ]\n  };\n}\n\nmodule.exports = zephir;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,MAAM;AACpB,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU,CAAE,KAAK,gBAAiB;AAAA,QAClC,UAAU;AAAA,UACR,KAAK,QAAQ,KAAK,kBAAkB;AAAA,YAClC,SAAS;AAAA,UACX,CAAC;AAAA,UACD,KAAK,QAAQ,KAAK,mBAAmB;AAAA,YACnC,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF;AACA,YAAM,aAAa,KAAK;AACxB,YAAM,SAAS;AAAA,QACb,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AACA,YAAM;AAAA;AAAA,QAEJ;AAAA;AAyBF,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,KAAM;AAAA,QACjB,UAAU;AAAA,QACV,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,UAAU;AAAA,gBACR;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAE,KAAK,gBAAiB;AAAA,UACpC;AAAA,UACA;AAAA;AAAA,YAEE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,UAAU;AAAA,gBACV,UAAU;AAAA,kBACR;AAAA,kBACA,KAAK;AAAA,kBACL;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,gBACE,eAAe;AAAA,cACjB;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU,CAAE,UAAW;AAAA,UACzB;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,KAAK;AAAA,YACL,UAAU,CAAE,UAAW;AAAA,UACzB;AAAA,UACA;AAAA,YACE,OAAO;AAAA;AAAA,UACT;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}