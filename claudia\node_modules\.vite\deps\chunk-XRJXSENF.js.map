{"version": 3, "sources": ["../../highlight.js/lib/languages/monkey.js"], "sourcesContent": ["/*\nLanguage: Monkey\nDescription: Monkey2 is an easy to use, cross platform, games oriented programming language from Blitz Research.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://blitzresearch.itch.io/monkey2\n*/\n\nfunction monkey(hljs) {\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      {\n        begin: '[$][a-fA-F0-9]+'\n      },\n      hljs.NUMBER_MODE\n    ]\n  };\n\n  return {\n    name: 'Monkey',\n    case_insensitive: true,\n    keywords: {\n      keyword: 'public private property continue exit extern new try catch ' +\n        'eachin not abstract final select case default const local global field ' +\n        'end if then else elseif endif while wend repeat until forever for ' +\n        'to step next return module inline throw import',\n\n      built_in: 'DebugLog DebugStop Error Print ACos ACosr ASin ASinr ATan ATan2 ATan2r ATanr Abs Abs Ceil ' +\n        'Clamp Clamp Cos Cosr Exp Floor Log Max Max Min Min Pow Sgn Sgn Sin Sinr Sqrt Tan Tanr Seed PI HALFPI TWOPI',\n\n      literal: 'true false null and or shl shr mod'\n    },\n    illegal: /\\/\\*/,\n    contains: [\n      hljs.COMMENT('#rem', '#end'),\n      hljs.COMMENT(\n        \"'\",\n        '$',\n        {\n          relevance: 0\n        }\n      ),\n      {\n        className: 'function',\n        beginKeywords: 'function method',\n        end: '[(=:]|$',\n        illegal: /\\n/,\n        contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'class interface',\n        end: '$',\n        contains: [\n          {\n            beginKeywords: 'extends implements'\n          },\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      {\n        className: 'built_in',\n        begin: '\\\\b(self|super)\\\\b'\n      },\n      {\n        className: 'meta',\n        begin: '\\\\s*#',\n        end: '$',\n        keywords: {\n          'meta-keyword': 'if else elseif endif end then'\n        }\n      },\n      {\n        className: 'meta',\n        begin: '^\\\\s*strict\\\\b'\n      },\n      {\n        beginKeywords: 'alias',\n        end: '=',\n        contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n      },\n      hljs.QUOTE_STRING_MODE,\n      NUMBER\n    ]\n  };\n}\n\nmodule.exports = monkey;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,OAAO,MAAM;AACpB,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA,KAAK;AAAA,QACP;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,UAAU;AAAA,UACR,SAAS;AAAA,UAKT,UAAU;AAAA,UAGV,SAAS;AAAA,QACX;AAAA,QACA,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK,QAAQ,QAAQ,MAAM;AAAA,UAC3B,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,WAAW;AAAA,YACb;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU,CAAE,KAAK,qBAAsB;AAAA,UACzC;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,gBACE,eAAe;AAAA,cACjB;AAAA,cACA,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR,gBAAgB;AAAA,YAClB;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,KAAK;AAAA,YACL,UAAU,CAAE,KAAK,qBAAsB;AAAA,UACzC;AAAA,UACA,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}