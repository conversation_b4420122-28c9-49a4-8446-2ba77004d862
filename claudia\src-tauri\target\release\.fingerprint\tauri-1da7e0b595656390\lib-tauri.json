{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"http-range\", \"image\", \"image-png\", \"protocol-asset\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 2040997289075261528, "path": 2627414824351317956, "deps": [[40386456601120721, "percent_encoding", false, 10394793211968261770], [442785307232013896, "tauri_runtime", false, 14058204282225953586], [1200537532907108615, "url<PERSON><PERSON>n", false, 10618455366097760799], [3150220818285335163, "url", false, 14171641885309381843], [4143744114649553716, "raw_window_handle", false, 14451994403637000321], [4341921533227644514, "muda", false, 10236175127469174568], [4919829919303820331, "serialize_to_javascript", false, 10260600393224614382], [5986029879202738730, "log", false, 16506617143423921061], [7752760652095876438, "tauri_runtime_wry", false, 2817445465374150851], [8351317599104215083, "tray_icon", false, 16179154150286064159], [8539587424388551196, "webview2_com", false, 3417196872854609185], [8866577183823226611, "http_range", false, 2090181767490278397], [9010263965687315507, "http", false, 12213520909095809930], [9228235415475680086, "tauri_macros", false, 8787815408914298337], [9538054652646069845, "tokio", false, 9712805951141166731], [9689903380558560274, "serde", false, 7541355907771353291], [9920160576179037441, "getrandom", false, 7798065846696137312], [10229185211513642314, "mime", false, 2474990561902353895], [10629569228670356391, "futures_util", false, 16760756696038921274], [10755362358622467486, "build_script_build", false, 1045948375113365585], [10806645703491011684, "thiserror", false, 5059168433112344807], [11050281405049894993, "tauri_utils", false, 9103384806753627340], [11989259058781683633, "dunce", false, 18275498118312540539], [12565293087094287914, "window_vibrancy", false, 10662934676956347128], [12986574360607194341, "serde_repr", false, 13655891311490039776], [13028763805764736075, "image", false, 8427077493849850405], [13077543566650298139, "heck", false, 13905543481018360176], [13116089016666501665, "windows", false, 8204701972585405957], [13625485746686963219, "anyhow", false, 12315510034218800024], [15367738274754116744, "serde_json", false, 17470562606684216992], [16928111194414003569, "dirs", false, 14231837660251147762], [17155886227862585100, "glob", false, 2983592991386580428]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-1da7e0b595656390\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}