{"version": 3, "sources": ["../../refractor/lang/gdscript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = gdscript\ngdscript.displayName = 'gdscript'\ngdscript.aliases = []\nfunction gdscript(Prism) {\n  Prism.languages.gdscript = {\n    comment: /#.*/,\n    string: {\n      pattern:\n        /@?(?:(\"|')(?:(?!\\1)[^\\n\\\\]|\\\\[\\s\\S])*\\1(?!\"|')|\"\"\"(?:[^\\\\]|\\\\[\\s\\S])*?\"\"\")/,\n      greedy: true\n    },\n    'class-name': {\n      // class_name Foo, extends Bar, class InnerClass\n      // export(int) var baz, export(int, 0) var i\n      // as Node\n      // const FOO: int = 9, var bar: bool = true\n      // func add(reference: Item, amount: int) -> Item:\n      pattern:\n        /(^(?:class|class_name|extends)[ \\t]+|^export\\([ \\t]*|\\bas[ \\t]+|(?:\\b(?:const|var)[ \\t]|[,(])[ \\t]*\\w+[ \\t]*:[ \\t]*|->[ \\t]*)[a-zA-Z_]\\w*/m,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:and|as|assert|break|breakpoint|class|class_name|const|continue|elif|else|enum|export|extends|for|func|if|in|is|master|mastersync|match|not|null|onready|or|pass|preload|puppet|puppetsync|remote|remotesync|return|self|setget|signal|static|tool|var|while|yield)\\b/,\n    function: /\\b[a-z_]\\w*(?=[ \\t]*\\()/i,\n    variable: /\\$\\w+/,\n    number: [\n      /\\b0b[01_]+\\b|\\b0x[\\da-fA-F_]+\\b|(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.[\\d_]+)(?:e[+-]?[\\d_]+)?\\b/,\n      /\\b(?:INF|NAN|PI|TAU)\\b/\n    ],\n    constant: /\\b[A-Z][A-Z_\\d]*\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    operator: /->|:=|&&|\\|\\||<<|>>|[-+*/%&|!<>=]=?|[~^]/,\n    punctuation: /[.:,;()[\\]{}]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,aAAS,cAAc;AACvB,aAAS,UAAU,CAAC;AACpB,aAAS,SAAS,OAAO;AACvB,YAAM,UAAU,WAAW;AAAA,QACzB,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,SACE;AAAA,UACF,QAAQ;AAAA,QACV;AAAA,QACA,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAMZ,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,SACE;AAAA,QACF,UAAU;AAAA,QACV,UAAU;AAAA,QACV,QAAQ;AAAA,UACN;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}