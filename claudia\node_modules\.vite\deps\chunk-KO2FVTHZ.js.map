{"version": 3, "sources": ["../../refractor/lang/csp.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = csp\ncsp.displayName = 'csp'\ncsp.aliases = []\nfunction csp(Prism) {\n  /**\n   * Original by <PERSON>.\n   *\n   * Reference: https://scotthelme.co.uk/csp-cheat-sheet/\n   *\n   * Supports the following:\n   *  - https://www.w3.org/TR/CSP1/\n   *  - https://www.w3.org/TR/CSP2/\n   *  - https://www.w3.org/TR/CSP3/\n   */\n  ;(function (Prism) {\n    /**\n     * @param {string} source\n     * @returns {RegExp}\n     */\n    function value(source) {\n      return RegExp(\n        /([ \\t])/.source + '(?:' + source + ')' + /(?=[\\s;]|$)/.source,\n        'i'\n      )\n    }\n    Prism.languages.csp = {\n      directive: {\n        pattern:\n          /(^|[\\s;])(?:base-uri|block-all-mixed-content|(?:child|connect|default|font|frame|img|manifest|media|object|prefetch|script|style|worker)-src|disown-opener|form-action|frame-(?:ancestors|options)|input-protection(?:-(?:clip|selectors))?|navigate-to|plugin-types|policy-uri|referrer|reflected-xss|report-(?:to|uri)|require-sri-for|sandbox|(?:script|style)-src-(?:attr|elem)|upgrade-insecure-requests)(?=[\\s;]|$)/i,\n        lookbehind: true,\n        alias: 'property'\n      },\n      scheme: {\n        pattern: value(/[a-z][a-z0-9.+-]*:/.source),\n        lookbehind: true\n      },\n      none: {\n        pattern: value(/'none'/.source),\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      nonce: {\n        pattern: value(/'nonce-[-+/\\w=]+'/.source),\n        lookbehind: true,\n        alias: 'number'\n      },\n      hash: {\n        pattern: value(/'sha(?:256|384|512)-[-+/\\w=]+'/.source),\n        lookbehind: true,\n        alias: 'number'\n      },\n      host: {\n        pattern: value(\n          /[a-z][a-z0-9.+-]*:\\/\\/[^\\s;,']*/.source +\n            '|' +\n            /\\*[^\\s;,']*/.source +\n            '|' +\n            /[a-z0-9-]+(?:\\.[a-z0-9-]+)+(?::[\\d*]+)?(?:\\/[^\\s;,']*)?/.source\n        ),\n        lookbehind: true,\n        alias: 'url',\n        inside: {\n          important: /\\*/\n        }\n      },\n      keyword: [\n        {\n          pattern: value(/'unsafe-[a-z-]+'/.source),\n          lookbehind: true,\n          alias: 'unsafe'\n        },\n        {\n          pattern: value(/'[a-z-]+'/.source),\n          lookbehind: true,\n          alias: 'safe'\n        }\n      ],\n      punctuation: /;/\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAWlB;AAAC,OAAC,SAAUA,QAAO;AAKjB,iBAAS,MAAM,QAAQ;AACrB,iBAAO;AAAA,YACL,UAAU,SAAS,QAAQ,SAAS,MAAM,cAAc;AAAA,YACxD;AAAA,UACF;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,MAAM;AAAA,UACpB,WAAW;AAAA,YACT,SACE;AAAA,YACF,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,QAAQ;AAAA,YACN,SAAS,MAAM,qBAAqB,MAAM;AAAA,YAC1C,YAAY;AAAA,UACd;AAAA,UACA,MAAM;AAAA,YACJ,SAAS,MAAM,SAAS,MAAM;AAAA,YAC9B,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,OAAO;AAAA,YACL,SAAS,MAAM,oBAAoB,MAAM;AAAA,YACzC,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,MAAM;AAAA,YACJ,SAAS,MAAM,iCAAiC,MAAM;AAAA,YACtD,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,MAAM;AAAA,YACJ,SAAS;AAAA,cACP,kCAAkC,SAChC,MACA,cAAc,SACd,MACA,0DAA0D;AAAA,YAC9D;AAAA,YACA,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,WAAW;AAAA,YACb;AAAA,UACF;AAAA,UACA,SAAS;AAAA,YACP;AAAA,cACE,SAAS,MAAM,mBAAmB,MAAM;AAAA,cACxC,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,SAAS,MAAM,YAAY,MAAM;AAAA,cACjC,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA,aAAa;AAAA,QACf;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}