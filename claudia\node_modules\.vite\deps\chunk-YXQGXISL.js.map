{"version": 3, "sources": ["../../highlight.js/lib/languages/jboss-cli.js"], "sourcesContent": ["/*\n Language: <PERSON><PERSON><PERSON> CLI\n Author: <PERSON><PERSON><PERSON> <<EMAIL>>\n Description: language definition jboss cli\n Website: https://docs.jboss.org/author/display/WFLY/Command+Line+Interface\n Category: config\n */\n\nfunction jbossCli(hljs) {\n  const PARAM = {\n    begin: /[\\w-]+ *=/,\n    returnBegin: true,\n    relevance: 0,\n    contains: [\n      {\n        className: 'attr',\n        begin: /[\\w-]+/\n      }\n    ]\n  };\n  const PARAMSBLOCK = {\n    className: 'params',\n    begin: /\\(/,\n    end: /\\)/,\n    contains: [PARAM],\n    relevance: 0\n  };\n  const OPERATION = {\n    className: 'function',\n    begin: /:[\\w\\-.]+/,\n    relevance: 0\n  };\n  const PATH = {\n    className: 'string',\n    begin: /\\B([\\/.])[\\w\\-.\\/=]+/\n  };\n  const COMMAND_PARAMS = {\n    className: 'params',\n    begin: /--[\\w\\-=\\/]+/\n  };\n  return {\n    name: '<PERSON><PERSON><PERSON> CLI',\n    aliases: ['wildfly-cli'],\n    keywords: {\n      $pattern: '[a-z\\-]+',\n      keyword: 'alias batch cd clear command connect connection-factory connection-info data-source deploy ' +\n      'deployment-info deployment-overlay echo echo-dmr help history if jdbc-driver-info jms-queue|20 jms-topic|20 ls ' +\n      'patch pwd quit read-attribute read-operation reload rollout-plan run-batch set shutdown try unalias ' +\n      'undeploy unset version xa-data-source', // module\n      literal: 'true false'\n    },\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      hljs.QUOTE_STRING_MODE,\n      COMMAND_PARAMS,\n      OPERATION,\n      PATH,\n      PARAMSBLOCK\n    ]\n  };\n}\n\nmodule.exports = jbossCli;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,SAAS,MAAM;AACtB,YAAM,QAAQ;AAAA,QACZ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAC,KAAK;AAAA,QAChB,WAAW;AAAA,MACb;AACA,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AACA,YAAM,OAAO;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,iBAAiB;AAAA,QACrB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,aAAa;AAAA,QACvB,UAAU;AAAA,UACR,UAAU;AAAA,UACV,SAAS;AAAA;AAAA,UAIT,SAAS;AAAA,QACX;AAAA,QACA,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}