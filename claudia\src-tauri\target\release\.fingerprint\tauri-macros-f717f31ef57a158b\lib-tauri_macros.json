{"rustc": 16591470773350601817, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 1369601567987815722, "path": 12121979343078292920, "deps": [[3060637413840920116, "proc_macro2", false, 8092708216756864826], [7341521034400937459, "tauri_codegen", false, 2803523430129046885], [11050281405049894993, "tauri_utils", false, 8077915665824671073], [13077543566650298139, "heck", false, 5014334375361847477], [17990358020177143287, "quote", false, 11228554297675514215], [18149961000318489080, "syn", false, 14376140178604925650]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-f717f31ef57a158b\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}