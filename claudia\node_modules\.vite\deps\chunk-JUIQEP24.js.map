{"version": 3, "sources": ["../../highlight.js/lib/languages/gherkin.js"], "sourcesContent": ["/*\n Language: Gherkin\n Author: <PERSON> (@pikesley) <<EMAIL>>\n Description: Gherkin is the format for cucumber specifications. It is a domain specific language which helps you to describe business behavior without the need to go into detail of implementation.\n Website: https://cucumber.io/docs/gherkin/\n */\n\nfunction gherkin(hljs) {\n  return {\n    name: '<PERSON>her<PERSON>',\n    aliases: ['feature'],\n    keywords: 'Feature Background Ability Business\\ Need Scenario Scenarios Scenario\\ Outline Scenario\\ Template Examples Given And Then But When',\n    contains: [\n      {\n        className: 'symbol',\n        begin: '\\\\*',\n        relevance: 0\n      },\n      {\n        className: 'meta',\n        begin: '@[^@\\\\s]+'\n      },\n      {\n        begin: '\\\\|',\n        end: '\\\\|\\\\w*$',\n        contains: [\n          {\n            className: 'string',\n            begin: '[^|]+'\n          }\n        ]\n      },\n      {\n        className: 'variable',\n        begin: '<',\n        end: '>'\n      },\n      hljs.HASH_COMMENT_MODE,\n      {\n        className: 'string',\n        begin: '\"\"\"',\n        end: '\"\"\"'\n      },\n      hljs.QUOTE_STRING_MODE\n    ]\n  };\n}\n\nmodule.exports = gherkin;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,QAAQ,MAAM;AACrB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,SAAS;AAAA,QACnB,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}