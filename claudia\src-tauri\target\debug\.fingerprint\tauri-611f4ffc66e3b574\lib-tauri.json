{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"http-range\", \"image\", \"image-png\", \"protocol-asset\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 2627414824351317956, "deps": [[40386456601120721, "percent_encoding", false, 9532286527671409682], [442785307232013896, "tauri_runtime", false, 10282853642438293076], [1200537532907108615, "url<PERSON><PERSON>n", false, 12785749307423811727], [3150220818285335163, "url", false, 7821896678146667634], [4143744114649553716, "raw_window_handle", false, 4590846174750262984], [4341921533227644514, "muda", false, 3339287744568877015], [4919829919303820331, "serialize_to_javascript", false, 2466763126379120841], [5986029879202738730, "log", false, 785926723577402214], [7752760652095876438, "tauri_runtime_wry", false, 3049352766080332546], [8351317599104215083, "tray_icon", false, 5416964313295305201], [8539587424388551196, "webview2_com", false, 15446265367576468344], [8866577183823226611, "http_range", false, 73777782553291073], [9010263965687315507, "http", false, 18131092521246687413], [9228235415475680086, "tauri_macros", false, 4348743270506310861], [9538054652646069845, "tokio", false, 14483482792075840282], [9689903380558560274, "serde", false, 11564652921496065999], [9920160576179037441, "getrandom", false, 120330377822985242], [10229185211513642314, "mime", false, 11402712181982263359], [10629569228670356391, "futures_util", false, 8612617707734785946], [10755362358622467486, "build_script_build", false, 6471864820242605517], [10806645703491011684, "thiserror", false, 5650462710600407025], [11050281405049894993, "tauri_utils", false, 12838874062673002299], [11989259058781683633, "dunce", false, 5514163782181656159], [12565293087094287914, "window_vibrancy", false, 11235038392105083172], [12986574360607194341, "serde_repr", false, 9858762501596638994], [13028763805764736075, "image", false, 5278653578130123881], [13077543566650298139, "heck", false, 13957943336384230101], [13116089016666501665, "windows", false, 3466355342227673606], [13625485746686963219, "anyhow", false, 12410288083040782734], [15367738274754116744, "serde_json", false, 6550851001671471717], [16928111194414003569, "dirs", false, 7602940668934922124], [17155886227862585100, "glob", false, 1271397756706627821]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-611f4ffc66e3b574\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}