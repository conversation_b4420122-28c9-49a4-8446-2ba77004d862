{"version": 3, "sources": ["../../refractor/lang/asciidoc.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = asciidoc\nasciidoc.displayName = 'asciidoc'\nasciidoc.aliases = ['adoc']\nfunction asciidoc(Prism) {\n  ;(function (Prism) {\n    var attributes = {\n      pattern:\n        /(^[ \\t]*)\\[(?!\\[)(?:([\"'$`])(?:(?!\\2)[^\\\\]|\\\\.)*\\2|\\[(?:[^\\[\\]\\\\]|\\\\.)*\\]|[^\\[\\]\\\\\"'$`]|\\\\.)*\\]/m,\n      lookbehind: true,\n      inside: {\n        quoted: {\n          pattern: /([$`])(?:(?!\\1)[^\\\\]|\\\\.)*\\1/,\n          inside: {\n            punctuation: /^[$`]|[$`]$/\n          }\n        },\n        interpreted: {\n          pattern: /'(?:[^'\\\\]|\\\\.)*'/,\n          inside: {\n            punctuation: /^'|'$/ // See rest below\n          }\n        },\n        string: /\"(?:[^\"\\\\]|\\\\.)*\"/,\n        variable: /\\w+(?==)/,\n        punctuation: /^\\[|\\]$|,/,\n        operator: /=/,\n        // The negative look-ahead prevents blank matches\n        'attr-value': /(?!^\\s+$).+/\n      }\n    }\n    var asciidoc = (Prism.languages.asciidoc = {\n      'comment-block': {\n        pattern: /^(\\/{4,})(?:\\r?\\n|\\r)(?:[\\s\\S]*(?:\\r?\\n|\\r))??\\1/m,\n        alias: 'comment'\n      },\n      table: {\n        pattern: /^\\|={3,}(?:(?:\\r?\\n|\\r(?!\\n)).*)*?(?:\\r?\\n|\\r)\\|={3,}$/m,\n        inside: {\n          specifiers: {\n            pattern:\n              /(?:(?:(?:\\d+(?:\\.\\d+)?|\\.\\d+)[+*](?:[<^>](?:\\.[<^>])?|\\.[<^>])?|[<^>](?:\\.[<^>])?|\\.[<^>])[a-z]*|[a-z]+)(?=\\|)/,\n            alias: 'attr-value'\n          },\n          punctuation: {\n            pattern: /(^|[^\\\\])[|!]=*/,\n            lookbehind: true\n          } // See rest below\n        }\n      },\n      'passthrough-block': {\n        pattern: /^(\\+{4,})(?:\\r?\\n|\\r)(?:[\\s\\S]*(?:\\r?\\n|\\r))??\\1$/m,\n        inside: {\n          punctuation: /^\\++|\\++$/ // See rest below\n        }\n      },\n      // Literal blocks and listing blocks\n      'literal-block': {\n        pattern: /^(-{4,}|\\.{4,})(?:\\r?\\n|\\r)(?:[\\s\\S]*(?:\\r?\\n|\\r))??\\1$/m,\n        inside: {\n          punctuation: /^(?:-+|\\.+)|(?:-+|\\.+)$/ // See rest below\n        }\n      },\n      // Sidebar blocks, quote blocks, example blocks and open blocks\n      'other-block': {\n        pattern:\n          /^(--|\\*{4,}|_{4,}|={4,})(?:\\r?\\n|\\r)(?:[\\s\\S]*(?:\\r?\\n|\\r))??\\1$/m,\n        inside: {\n          punctuation: /^(?:-+|\\*+|_+|=+)|(?:-+|\\*+|_+|=+)$/ // See rest below\n        }\n      },\n      // list-punctuation and list-label must appear before indented-block\n      'list-punctuation': {\n        pattern:\n          /(^[ \\t]*)(?:-|\\*{1,5}|\\.{1,5}|(?:[a-z]|\\d+)\\.|[xvi]+\\))(?= )/im,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      'list-label': {\n        pattern: /(^[ \\t]*)[a-z\\d].+(?::{2,4}|;;)(?=\\s)/im,\n        lookbehind: true,\n        alias: 'symbol'\n      },\n      'indented-block': {\n        pattern: /((\\r?\\n|\\r)\\2)([ \\t]+)\\S.*(?:(?:\\r?\\n|\\r)\\3.+)*(?=\\2{2}|$)/,\n        lookbehind: true\n      },\n      comment: /^\\/\\/.*/m,\n      title: {\n        pattern:\n          /^.+(?:\\r?\\n|\\r)(?:={3,}|-{3,}|~{3,}|\\^{3,}|\\+{3,})$|^={1,5} .+|^\\.(?![\\s.]).*/m,\n        alias: 'important',\n        inside: {\n          punctuation: /^(?:\\.|=+)|(?:=+|-+|~+|\\^+|\\++)$/ // See rest below\n        }\n      },\n      'attribute-entry': {\n        pattern: /^:[^:\\r\\n]+:(?: .*?(?: \\+(?:\\r?\\n|\\r).*?)*)?$/m,\n        alias: 'tag'\n      },\n      attributes: attributes,\n      hr: {\n        pattern: /^'{3,}$/m,\n        alias: 'punctuation'\n      },\n      'page-break': {\n        pattern: /^<{3,}$/m,\n        alias: 'punctuation'\n      },\n      admonition: {\n        pattern: /^(?:CAUTION|IMPORTANT|NOTE|TIP|WARNING):/m,\n        alias: 'keyword'\n      },\n      callout: [\n        {\n          pattern: /(^[ \\t]*)<?\\d*>/m,\n          lookbehind: true,\n          alias: 'symbol'\n        },\n        {\n          pattern: /<\\d+>/,\n          alias: 'symbol'\n        }\n      ],\n      macro: {\n        pattern:\n          /\\b[a-z\\d][a-z\\d-]*::?(?:[^\\s\\[\\]]*\\[(?:[^\\]\\\\\"']|([\"'])(?:(?!\\1)[^\\\\]|\\\\.)*\\1|\\\\.)*\\])/,\n        inside: {\n          function: /^[a-z\\d-]+(?=:)/,\n          punctuation: /^::?/,\n          attributes: {\n            pattern: /(?:\\[(?:[^\\]\\\\\"']|([\"'])(?:(?!\\1)[^\\\\]|\\\\.)*\\1|\\\\.)*\\])/,\n            inside: attributes.inside\n          }\n        }\n      },\n      inline: {\n        /*\nThe initial look-behind prevents the highlighting of escaped quoted text.\nQuoted text can be multi-line but cannot span an empty line.\nAll quoted text can have attributes before [foobar, 'foobar', baz=\"bar\"].\nFirst, we handle the constrained quotes.\nThose must be bounded by non-word chars and cannot have spaces between the delimiter and the first char.\nThey are, in order: _emphasis_, ``double quotes'', `single quotes', `monospace`, 'emphasis', *strong*, +monospace+ and #unquoted#\nThen we handle the unconstrained quotes.\nThose do not have the restrictions of the constrained quotes.\nThey are, in order: __emphasis__, **strong**, ++monospace++, +++passthrough+++, ##unquoted##, $$passthrough$$, ~subscript~, ^superscript^, {attribute-reference}, [[anchor]], [[[bibliography anchor]]], <<xref>>, (((indexes))) and ((indexes))\n*/\n        pattern:\n          /(^|[^\\\\])(?:(?:\\B\\[(?:[^\\]\\\\\"']|([\"'])(?:(?!\\2)[^\\\\]|\\\\.)*\\2|\\\\.)*\\])?(?:\\b_(?!\\s)(?: _|[^_\\\\\\r\\n]|\\\\.)+(?:(?:\\r?\\n|\\r)(?: _|[^_\\\\\\r\\n]|\\\\.)+)*_\\b|\\B``(?!\\s).+?(?:(?:\\r?\\n|\\r).+?)*''\\B|\\B`(?!\\s)(?:[^`'\\s]|\\s+\\S)+['`]\\B|\\B(['*+#])(?!\\s)(?: \\3|(?!\\3)[^\\\\\\r\\n]|\\\\.)+(?:(?:\\r?\\n|\\r)(?: \\3|(?!\\3)[^\\\\\\r\\n]|\\\\.)+)*\\3\\B)|(?:\\[(?:[^\\]\\\\\"']|([\"'])(?:(?!\\4)[^\\\\]|\\\\.)*\\4|\\\\.)*\\])?(?:(__|\\*\\*|\\+\\+\\+?|##|\\$\\$|[~^]).+?(?:(?:\\r?\\n|\\r).+?)*\\5|\\{[^}\\r\\n]+\\}|\\[\\[\\[?.+?(?:(?:\\r?\\n|\\r).+?)*\\]?\\]\\]|<<.+?(?:(?:\\r?\\n|\\r).+?)*>>|\\(\\(\\(?.+?(?:(?:\\r?\\n|\\r).+?)*\\)?\\)\\)))/m,\n        lookbehind: true,\n        inside: {\n          attributes: attributes,\n          url: {\n            pattern: /^(?:\\[\\[\\[?.+?\\]?\\]\\]|<<.+?>>)$/,\n            inside: {\n              punctuation: /^(?:\\[\\[\\[?|<<)|(?:\\]\\]\\]?|>>)$/\n            }\n          },\n          'attribute-ref': {\n            pattern: /^\\{.+\\}$/,\n            inside: {\n              variable: {\n                pattern: /(^\\{)[a-z\\d,+_-]+/,\n                lookbehind: true\n              },\n              operator: /^[=?!#%@$]|!(?=[:}])/,\n              punctuation: /^\\{|\\}$|::?/\n            }\n          },\n          italic: {\n            pattern: /^(['_])[\\s\\S]+\\1$/,\n            inside: {\n              punctuation: /^(?:''?|__?)|(?:''?|__?)$/\n            }\n          },\n          bold: {\n            pattern: /^\\*[\\s\\S]+\\*$/,\n            inside: {\n              punctuation: /^\\*\\*?|\\*\\*?$/\n            }\n          },\n          punctuation:\n            /^(?:``?|\\+{1,3}|##?|\\$\\$|[~^]|\\(\\(\\(?)|(?:''?|\\+{1,3}|##?|\\$\\$|[~^`]|\\)?\\)\\))$/\n        }\n      },\n      replacement: {\n        pattern: /\\((?:C|R|TM)\\)/,\n        alias: 'builtin'\n      },\n      entity: /&#?[\\da-z]{1,8};/i,\n      'line-continuation': {\n        pattern: /(^| )\\+$/m,\n        lookbehind: true,\n        alias: 'punctuation'\n      }\n    }) // Allow some nesting. There is no recursion though, so cloning should not be needed.\n    function copyFromAsciiDoc(keys) {\n      keys = keys.split(' ')\n      var o = {}\n      for (var i = 0, l = keys.length; i < l; i++) {\n        o[keys[i]] = asciidoc[keys[i]]\n      }\n      return o\n    }\n    attributes.inside['interpreted'].inside.rest = copyFromAsciiDoc(\n      'macro inline replacement entity'\n    )\n    asciidoc['passthrough-block'].inside.rest = copyFromAsciiDoc('macro')\n    asciidoc['literal-block'].inside.rest = copyFromAsciiDoc('callout')\n    asciidoc['table'].inside.rest = copyFromAsciiDoc(\n      'comment-block passthrough-block literal-block other-block list-punctuation indented-block comment title attribute-entry attributes hr page-break admonition list-label callout macro inline replacement entity line-continuation'\n    )\n    asciidoc['other-block'].inside.rest = copyFromAsciiDoc(\n      'table list-punctuation indented-block comment attribute-entry attributes hr page-break admonition list-label macro inline replacement entity line-continuation'\n    )\n    asciidoc['title'].inside.rest = copyFromAsciiDoc(\n      'macro inline replacement entity'\n    ) // Plugin to make entity title show the real entity, idea by Roman Komarov\n    Prism.hooks.add('wrap', function (env) {\n      if (env.type === 'entity') {\n        env.attributes['title'] = env.content.value.replace(/&amp;/, '&')\n      }\n    })\n    Prism.languages.adoc = Prism.languages.asciidoc\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,aAAS,cAAc;AACvB,aAAS,UAAU,CAAC,MAAM;AAC1B,aAAS,SAAS,OAAO;AACvB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,aAAa;AAAA,UACf,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,QAAQ;AAAA,cACN,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA,aAAa;AAAA,cACX,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,aAAa;AAAA;AAAA,cACf;AAAA,YACF;AAAA,YACA,QAAQ;AAAA,YACR,UAAU;AAAA,YACV,aAAa;AAAA,YACb,UAAU;AAAA;AAAA,YAEV,cAAc;AAAA,UAChB;AAAA,QACF;AACA,YAAIC,YAAYD,OAAM,UAAU,WAAW;AAAA,UACzC,iBAAiB;AAAA,YACf,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,OAAO;AAAA,YACL,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,YAAY;AAAA,gBACV,SACE;AAAA,gBACF,OAAO;AAAA,cACT;AAAA,cACA,aAAa;AAAA,gBACX,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,qBAAqB;AAAA,YACnB,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,aAAa;AAAA;AAAA,YACf;AAAA,UACF;AAAA;AAAA,UAEA,iBAAiB;AAAA,YACf,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,aAAa;AAAA;AAAA,YACf;AAAA,UACF;AAAA;AAAA,UAEA,eAAe;AAAA,YACb,SACE;AAAA,YACF,QAAQ;AAAA,cACN,aAAa;AAAA;AAAA,YACf;AAAA,UACF;AAAA;AAAA,UAEA,oBAAoB;AAAA,YAClB,SACE;AAAA,YACF,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,cAAc;AAAA,YACZ,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,kBAAkB;AAAA,YAChB,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,SAAS;AAAA,UACT,OAAO;AAAA,YACL,SACE;AAAA,YACF,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,aAAa;AAAA;AAAA,YACf;AAAA,UACF;AAAA,UACA,mBAAmB;AAAA,YACjB,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA;AAAA,UACA,IAAI;AAAA,YACF,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,cAAc;AAAA,YACZ,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,YAAY;AAAA,YACV,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,SAAS;AAAA,YACP;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA,OAAO;AAAA,YACL,SACE;AAAA,YACF,QAAQ;AAAA,cACN,UAAU;AAAA,cACV,aAAa;AAAA,cACb,YAAY;AAAA,gBACV,SAAS;AAAA,gBACT,QAAQ,WAAW;AAAA,cACrB;AAAA,YACF;AAAA,UACF;AAAA,UACA,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAYN,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN;AAAA,cACA,KAAK;AAAA,gBACH,SAAS;AAAA,gBACT,QAAQ;AAAA,kBACN,aAAa;AAAA,gBACf;AAAA,cACF;AAAA,cACA,iBAAiB;AAAA,gBACf,SAAS;AAAA,gBACT,QAAQ;AAAA,kBACN,UAAU;AAAA,oBACR,SAAS;AAAA,oBACT,YAAY;AAAA,kBACd;AAAA,kBACA,UAAU;AAAA,kBACV,aAAa;AAAA,gBACf;AAAA,cACF;AAAA,cACA,QAAQ;AAAA,gBACN,SAAS;AAAA,gBACT,QAAQ;AAAA,kBACN,aAAa;AAAA,gBACf;AAAA,cACF;AAAA,cACA,MAAM;AAAA,gBACJ,SAAS;AAAA,gBACT,QAAQ;AAAA,kBACN,aAAa;AAAA,gBACf;AAAA,cACF;AAAA,cACA,aACE;AAAA,YACJ;AAAA,UACF;AAAA,UACA,aAAa;AAAA,YACX,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,QAAQ;AAAA,UACR,qBAAqB;AAAA,YACnB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,QACF;AACA,iBAAS,iBAAiB,MAAM;AAC9B,iBAAO,KAAK,MAAM,GAAG;AACrB,cAAI,IAAI,CAAC;AACT,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AAC3C,cAAE,KAAK,CAAC,CAAC,IAAIC,UAAS,KAAK,CAAC,CAAC;AAAA,UAC/B;AACA,iBAAO;AAAA,QACT;AACA,mBAAW,OAAO,aAAa,EAAE,OAAO,OAAO;AAAA,UAC7C;AAAA,QACF;AACA,QAAAA,UAAS,mBAAmB,EAAE,OAAO,OAAO,iBAAiB,OAAO;AACpE,QAAAA,UAAS,eAAe,EAAE,OAAO,OAAO,iBAAiB,SAAS;AAClE,QAAAA,UAAS,OAAO,EAAE,OAAO,OAAO;AAAA,UAC9B;AAAA,QACF;AACA,QAAAA,UAAS,aAAa,EAAE,OAAO,OAAO;AAAA,UACpC;AAAA,QACF;AACA,QAAAA,UAAS,OAAO,EAAE,OAAO,OAAO;AAAA,UAC9B;AAAA,QACF;AACA,QAAAD,OAAM,MAAM,IAAI,QAAQ,SAAU,KAAK;AACrC,cAAI,IAAI,SAAS,UAAU;AACzB,gBAAI,WAAW,OAAO,IAAI,IAAI,QAAQ,MAAM,QAAQ,SAAS,GAAG;AAAA,UAClE;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,OAAOA,OAAM,UAAU;AAAA,MACzC,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism", "asciidoc"]}