import {
  require_core
} from "./chunk-72XENH2D.js";
import {
  require_wolfram
} from "./chunk-FGOZJBRF.js";
import {
  require_wren
} from "./chunk-6MQNH6RM.js";
import {
  require_xeora
} from "./chunk-XXIYS73K.js";
import {
  require_xml_doc
} from "./chunk-56G54LWQ.js";
import {
  require_xojo
} from "./chunk-VGU7C4EC.js";
import {
  require_xquery
} from "./chunk-3D64MBCF.js";
import {
  require_yang
} from "./chunk-RHM7UODB.js";
import {
  require_zig
} from "./chunk-AQFEA2R3.js";
import {
  require_verilog
} from "./chunk-QWWDM6A6.js";
import {
  require_vhdl
} from "./chunk-ZVK3WNJ4.js";
import {
  require_vim
} from "./chunk-BLQJL3I2.js";
import {
  require_visual_basic
} from "./chunk-AM62ONZ6.js";
import {
  require_warpscript
} from "./chunk-Y247IQPW.js";
import {
  require_wasm
} from "./chunk-5AW2B4AN.js";
import {
  require_web_idl
} from "./chunk-OLBOZN2O.js";
import {
  require_wiki
} from "./chunk-L26UCR5B.js";
import {
  require_twig
} from "./chunk-NIJWKC5F.js";
import {
  require_typoscript
} from "./chunk-O7UBISZ5.js";
import {
  require_unrealscript
} from "./chunk-YSQVWM7V.js";
import {
  require_uorazor
} from "./chunk-D4D36Z6T.js";
import {
  require_uri
} from "./chunk-D7VR453E.js";
import {
  require_v
} from "./chunk-K7XFAXNE.js";
import {
  require_vala
} from "./chunk-RWAYWS2X.js";
import {
  require_velocity
} from "./chunk-HJNQBVNF.js";
import {
  require_tap
} from "./chunk-HX4X3IFX.js";
import {
  require_yaml
} from "./chunk-NTE2Z5DJ.js";
import {
  require_tcl
} from "./chunk-ANHUGB4D.js";
import {
  require_textile
} from "./chunk-LCT347BW.js";
import {
  require_toml
} from "./chunk-TEBDYVIB.js";
import {
  require_tremor
} from "./chunk-IAT5ZRY7.js";
import {
  require_tsx
} from "./chunk-CIB7R24X.js";
import {
  require_tt2
} from "./chunk-SBCKZXDQ.js";
import {
  require_stan
} from "./chunk-QEQWH4OS.js";
import {
  require_stylus
} from "./chunk-3DXGDFAQ.js";
import {
  require_swift
} from "./chunk-PLP2A2CP.js";
import {
  require_systemd
} from "./chunk-VNQXQ44D.js";
import {
  require_t4_cs
} from "./chunk-Q4PEACJ7.js";
import {
  require_t4_vb
} from "./chunk-H546HK7K.js";
import {
  require_t4_templating
} from "./chunk-Q3JX5DFA.js";
import {
  require_vbnet
} from "./chunk-H6JJBY3N.js";
import {
  require_solidity
} from "./chunk-6Z7LQB5Q.js";
import {
  require_solution_file
} from "./chunk-ZTYPDMGT.js";
import {
  require_soy
} from "./chunk-MXD54OVE.js";
import {
  require_sparql
} from "./chunk-DHGOQJ26.js";
import {
  require_turtle
} from "./chunk-FRG6LCRU.js";
import {
  require_splunk_spl
} from "./chunk-HG4BRRC7.js";
import {
  require_sqf
} from "./chunk-SZNG7WS3.js";
import {
  require_squirrel
} from "./chunk-IRMUVKO3.js";
import {
  require_sass
} from "./chunk-MWIRPHKB.js";
import {
  require_scala
} from "./chunk-DT23X2U5.js";
import {
  require_scss
} from "./chunk-VXW64RVB.js";
import {
  require_shell_session
} from "./chunk-LOYAZTSF.js";
import {
  require_smali
} from "./chunk-TY2VYGUJ.js";
import {
  require_smalltalk
} from "./chunk-EMUXXHZD.js";
import {
  require_smarty
} from "./chunk-JSTOVVFE.js";
import {
  require_sml
} from "./chunk-G37POPYB.js";
import {
  require_rego
} from "./chunk-WBHHGY3A.js";
import {
  require_renpy
} from "./chunk-5KSTDNRB.js";
import {
  require_rest
} from "./chunk-5ZKJ7A22.js";
import {
  require_rip
} from "./chunk-LWTJVAP3.js";
import {
  require_roboconf
} from "./chunk-Z6TFJNQP.js";
import {
  require_robotframework
} from "./chunk-QUW5B6DX.js";
import {
  require_rust
} from "./chunk-CMLE6ZUO.js";
import {
  require_sas
} from "./chunk-KFSNSKWZ.js";
import {
  require_q
} from "./chunk-XRQPH2S5.js";
import {
  require_qml
} from "./chunk-HY7IDB4S.js";
import {
  require_qore
} from "./chunk-YWN65WKJ.js";
import {
  require_qsharp
} from "./chunk-E7Z3PL3R.js";
import {
  require_r
} from "./chunk-RWHQA3LX.js";
import {
  require_racket
} from "./chunk-J6DBRXWY.js";
import {
  require_reason
} from "./chunk-EX7QN45S.js";
import {
  require_regex
} from "./chunk-EQP7ZTVR.js";
import {
  require_protobuf
} from "./chunk-F2RG6KT3.js";
import {
  require_psl
} from "./chunk-HYWA26DP.js";
import {
  require_pug
} from "./chunk-3CXMRZRZ.js";
import {
  require_puppet
} from "./chunk-Y6SSEIZW.js";
import {
  require_pure
} from "./chunk-EK72IJWA.js";
import {
  require_purebasic
} from "./chunk-AS5TIR6P.js";
import {
  require_purescript
} from "./chunk-3UQMODN6.js";
import {
  require_python
} from "./chunk-VXS5VRY7.js";
import {
  require_phpdoc
} from "./chunk-V6DLP3BE.js";
import {
  require_plsql
} from "./chunk-HMK6DGQN.js";
import {
  require_powerquery
} from "./chunk-RYHHI3D2.js";
import {
  require_powershell
} from "./chunk-OIZM7JVG.js";
import {
  require_processing
} from "./chunk-UJDGFVPP.js";
import {
  require_prolog
} from "./chunk-55ZKL3PQ.js";
import {
  require_promql
} from "./chunk-PGRELWHC.js";
import {
  require_properties
} from "./chunk-Z7A2Y5QO.js";
import {
  require_parigp
} from "./chunk-QTKWGUW5.js";
import {
  require_parser
} from "./chunk-KAX6MLXD.js";
import {
  require_pascal
} from "./chunk-46DKC6Q7.js";
import {
  require_pascaligo
} from "./chunk-N4NBFW35.js";
import {
  require_pcaxis
} from "./chunk-64VSJI2F.js";
import {
  require_peoplecode
} from "./chunk-IFMHABDQ.js";
import {
  require_perl
} from "./chunk-53VXJLPF.js";
import {
  require_php_extras
} from "./chunk-6D2H6MJ2.js";
import {
  require_nim
} from "./chunk-R76LJ6TT.js";
import {
  require_nix
} from "./chunk-ADGGN57U.js";
import {
  require_nsis
} from "./chunk-3CLX6GGD.js";
import {
  require_objectivec
} from "./chunk-QCCHCAV5.js";
import {
  require_ocaml
} from "./chunk-OWMI6AGF.js";
import {
  require_opencl
} from "./chunk-PGWMQQNX.js";
import {
  require_openqasm
} from "./chunk-DB4PK6ZS.js";
import {
  require_oz
} from "./chunk-PW4QQ3OM.js";
import {
  require_n1ql
} from "./chunk-EVX4PP6T.js";
import {
  require_n4js
} from "./chunk-Q6UUI7C4.js";
import {
  require_nand2tetris_hdl
} from "./chunk-R7OWMQJM.js";
import {
  require_naniscript
} from "./chunk-4SQ2FAA3.js";
import {
  require_nasm
} from "./chunk-QEITC7T4.js";
import {
  require_neon
} from "./chunk-ICEIPU7D.js";
import {
  require_nevod
} from "./chunk-EWKGR4HU.js";
import {
  require_nginx
} from "./chunk-EE72IOTD.js";
import {
  require_matlab
} from "./chunk-7FRPJWAX.js";
import {
  require_maxscript
} from "./chunk-4OKSKYSH.js";
import {
  require_mel
} from "./chunk-EAKBXJBS.js";
import {
  require_mermaid
} from "./chunk-W6LOWXY2.js";
import {
  require_mizar
} from "./chunk-FSZCVKXC.js";
import {
  require_mongodb
} from "./chunk-WGT62MXH.js";
import {
  require_monkey
} from "./chunk-AEWZRHFE.js";
import {
  require_moonscript
} from "./chunk-TJAG7QPV.js";
import {
  require_livescript
} from "./chunk-PCIGRUN7.js";
import {
  require_llvm
} from "./chunk-IBXRVIVZ.js";
import {
  require_log
} from "./chunk-HXQ4JRUM.js";
import {
  require_lolcode
} from "./chunk-Z6BDNDZB.js";
import {
  require_magma
} from "./chunk-U5ZBOZRM.js";
import {
  require_makefile
} from "./chunk-XOTDT7DH.js";
import {
  require_markdown
} from "./chunk-ROLKSYZU.js";
import {
  require_latex
} from "./chunk-P5LCWPUM.js";
import {
  require_latte
} from "./chunk-TLAPL7QE.js";
import {
  require_php
} from "./chunk-57WN4AZA.js";
import {
  require_less
} from "./chunk-X3BGN4MV.js";
import {
  require_lilypond
} from "./chunk-X7QMVBB3.js";
import {
  require_scheme
} from "./chunk-XHIG33L5.js";
import {
  require_liquid
} from "./chunk-3XLLGNEZ.js";
import {
  require_lisp
} from "./chunk-4HYDISQJ.js";
import {
  require_jsstacktrace
} from "./chunk-72JL2HAD.js";
import {
  require_jsx
} from "./chunk-QDMEXYXN.js";
import {
  require_julia
} from "./chunk-VPS7C5XU.js";
import {
  require_keepalived
} from "./chunk-EGLJEOUS.js";
import {
  require_keyman
} from "./chunk-NUOL3S6P.js";
import {
  require_kotlin
} from "./chunk-6EOWA43B.js";
import {
  require_kumir
} from "./chunk-7RWO2JCL.js";
import {
  require_kusto
} from "./chunk-M2R2WVI6.js";
import {
  require_jq
} from "./chunk-DLJPFUOV.js";
import {
  require_js_extras
} from "./chunk-HAQPX3RL.js";
import {
  require_js_templates
} from "./chunk-GZLWFR5S.js";
import {
  require_jsdoc
} from "./chunk-U5JT5CMM.js";
import {
  require_typescript
} from "./chunk-7ZYHVL4Z.js";
import {
  require_json5
} from "./chunk-FHZ6PBQO.js";
import {
  require_jsonp
} from "./chunk-W5236IWR.js";
import {
  require_json
} from "./chunk-MHIHWJSM.js";
import {
  require_j
} from "./chunk-YI3M75NH.js";
import {
  require_javadoc
} from "./chunk-XCOYRC5T.js";
import {
  require_java
} from "./chunk-IPFONVON.js";
import {
  require_javadoclike
} from "./chunk-K45FC4T4.js";
import {
  require_javastacktrace
} from "./chunk-T4MKRSKM.js";
import {
  require_jexl
} from "./chunk-7FWEVTMY.js";
import {
  require_jolie
} from "./chunk-AH6AAYKU.js";
import {
  require_icon
} from "./chunk-HBSYVTQC.js";
import {
  require_icu_message_format
} from "./chunk-SFU7NQ64.js";
import {
  require_idris
} from "./chunk-NUBX4PFH.js";
import {
  require_iecst
} from "./chunk-CLLQRCNO.js";
import {
  require_ignore
} from "./chunk-U32LRPTE.js";
import {
  require_inform7
} from "./chunk-VBDY6VVO.js";
import {
  require_ini
} from "./chunk-JNXR27TQ.js";
import {
  require_io
} from "./chunk-NFBKACAI.js";
import {
  require_haxe
} from "./chunk-VM6YN6CD.js";
import {
  require_hcl
} from "./chunk-GLP7XRZ3.js";
import {
  require_hlsl
} from "./chunk-ONPSNAGN.js";
import {
  require_hoon
} from "./chunk-RFDRLT6J.js";
import {
  require_hpkp
} from "./chunk-7TEFRPGL.js";
import {
  require_hsts
} from "./chunk-Z76DK5MU.js";
import {
  require_http
} from "./chunk-V37C2H4Z.js";
import {
  require_ichigojam
} from "./chunk-VLC6U4AB.js";
import {
  require_gn
} from "./chunk-M5KLBZ6O.js";
import {
  require_go_module
} from "./chunk-C4V3QBJF.js";
import {
  require_go
} from "./chunk-3EFPBRMI.js";
import {
  require_graphql
} from "./chunk-ZKZR2LR7.js";
import {
  require_groovy
} from "./chunk-WWRC6T6Y.js";
import {
  require_haml
} from "./chunk-LT3ELPGB.js";
import {
  require_handlebars
} from "./chunk-VA72OTWY.js";
import {
  require_haskell
} from "./chunk-KDVCMQIN.js";
import {
  require_gap
} from "./chunk-3HNMWM4V.js";
import {
  require_gcode
} from "./chunk-H2LNHI6Z.js";
import {
  require_gdscript
} from "./chunk-JREZPXMZ.js";
import {
  require_gedcom
} from "./chunk-4PRZCQZL.js";
import {
  require_gherkin
} from "./chunk-EWQYLIT6.js";
import {
  require_git
} from "./chunk-BULMBG5W.js";
import {
  require_glsl
} from "./chunk-YUDBFWKA.js";
import {
  require_gml
} from "./chunk-4BSPF2DY.js";
import {
  require_excel_formula
} from "./chunk-UCNBUCK3.js";
import {
  require_factor
} from "./chunk-NY5ZXXCG.js";
import {
  require_false
} from "./chunk-DVTHDNHB.js";
import {
  require_firestore_security_rules
} from "./chunk-ISX3WRY6.js";
import {
  require_flow
} from "./chunk-2GYGGERK.js";
import {
  require_fortran
} from "./chunk-D54OPRUG.js";
import {
  require_fsharp
} from "./chunk-RRLD3272.js";
import {
  require_ftl
} from "./chunk-MFDJK6KI.js";
import {
  require_eiffel
} from "./chunk-IWLYCICB.js";
import {
  require_ejs
} from "./chunk-CWNLAKLJ.js";
import {
  require_elixir
} from "./chunk-3HH7PPG2.js";
import {
  require_elm
} from "./chunk-VQ5A6YFG.js";
import {
  require_erb
} from "./chunk-PV3G6BYV.js";
import {
  require_erlang
} from "./chunk-LD7SJAHK.js";
import {
  require_etlua
} from "./chunk-AHLLWPDZ.js";
import {
  require_lua
} from "./chunk-KFYITKIT.js";
import {
  require_diff
} from "./chunk-M7WC62WY.js";
import {
  require_django
} from "./chunk-UOGOOAG4.js";
import {
  require_markup_templating
} from "./chunk-GK7VE4GW.js";
import {
  require_dns_zone_file
} from "./chunk-IKWQ2M5T.js";
import {
  require_docker
} from "./chunk-SFJNAXTS.js";
import {
  require_dot
} from "./chunk-ADJ5WHXG.js";
import {
  require_ebnf
} from "./chunk-GIMQ7X2I.js";
import {
  require_editorconfig
} from "./chunk-BSLUDGMU.js";
import {
  require_csv
} from "./chunk-LWCNRVMZ.js";
import {
  require_cypher
} from "./chunk-C2XJ6QHQ.js";
import {
  require_d
} from "./chunk-Z5HZS6OE.js";
import {
  require_dart
} from "./chunk-BQL2BZCF.js";
import {
  require_dataweave
} from "./chunk-ETZGEU6W.js";
import {
  require_dax
} from "./chunk-SO3RA76C.js";
import {
  require_dhall
} from "./chunk-BDOPVPM6.js";
import {
  require_coffeescript
} from "./chunk-DIPR2TOW.js";
import {
  require_concurnas
} from "./chunk-2QUJ2CP6.js";
import {
  require_coq
} from "./chunk-O7KDSBKE.js";
import {
  require_crystal
} from "./chunk-BV4PHSR5.js";
import {
  require_ruby
} from "./chunk-OMXOVTVW.js";
import {
  require_cshtml
} from "./chunk-KW7IGTEV.js";
import {
  require_csp
} from "./chunk-KO2FVTHZ.js";
import {
  require_css_extras
} from "./chunk-VBT67LCB.js";
import {
  require_bsl
} from "./chunk-ZRF6QGYK.js";
import {
  require_cfscript
} from "./chunk-QOXJ5EDX.js";
import {
  require_chaiscript
} from "./chunk-5KZSZHMB.js";
import {
  require_cil
} from "./chunk-MSU6ZFCK.js";
import {
  require_clojure
} from "./chunk-SQJ3BGHF.js";
import {
  require_cmake
} from "./chunk-QFVA4WEA.js";
import {
  require_cobol
} from "./chunk-GP4BETZY.js";
import {
  require_bbcode
} from "./chunk-YK2N25IG.js";
import {
  require_bicep
} from "./chunk-OUDWHFTI.js";
import {
  require_birb
} from "./chunk-H6AOMMTE.js";
import {
  require_bison
} from "./chunk-3FHFAL22.js";
import {
  require_bnf
} from "./chunk-U5VMVEQD.js";
import {
  require_brainfuck
} from "./chunk-NIVUXLT5.js";
import {
  require_brightscript
} from "./chunk-53762T3A.js";
import {
  require_bro
} from "./chunk-ICNKKYZ2.js";
import {
  require_aspnet
} from "./chunk-WIAWNFBB.js";
import {
  require_autohotkey
} from "./chunk-GV7Q2IPX.js";
import {
  require_autoit
} from "./chunk-WX74NC2O.js";
import {
  require_avisynth
} from "./chunk-D6PTVO7C.js";
import {
  require_avro_idl
} from "./chunk-PK4YCKQ6.js";
import {
  require_bash
} from "./chunk-GXDZ2SZS.js";
import {
  require_basic
} from "./chunk-GNV73VXD.js";
import {
  require_batch
} from "./chunk-Z562RYSC.js";
import {
  require_arduino
} from "./chunk-CSSACTPI.js";
import {
  require_cpp
} from "./chunk-3CCXIGER.js";
import {
  require_c
} from "./chunk-VUWMSVMX.js";
import {
  require_arff
} from "./chunk-K3U2AV4N.js";
import {
  require_asciidoc
} from "./chunk-PQDBOY5W.js";
import {
  require_asm6502
} from "./chunk-IRAOXXAR.js";
import {
  require_asmatmel
} from "./chunk-ZIMNDQNM.js";
import {
  require_csharp
} from "./chunk-MEVVINGA.js";
import {
  require_al
} from "./chunk-LY3V2WHA.js";
import {
  require_antlr4
} from "./chunk-5DTPR7WA.js";
import {
  require_apacheconf
} from "./chunk-ZR2CWJ5V.js";
import {
  require_apex
} from "./chunk-5TZRBDEK.js";
import {
  require_sql
} from "./chunk-IOOEKUXV.js";
import {
  require_apl
} from "./chunk-NW23MUAM.js";
import {
  require_applescript
} from "./chunk-C24NBDJB.js";
import {
  require_aql
} from "./chunk-VWDERS4H.js";
import {
  require_abap
} from "./chunk-3KZZMOFP.js";
import {
  require_abnf
} from "./chunk-UBODCKMV.js";
import {
  require_actionscript
} from "./chunk-RE6PUU24.js";
import {
  require_ada
} from "./chunk-THYO7TA6.js";
import {
  require_agda
} from "./chunk-5F6QRVG6.js";
import {
  __commonJS
} from "./chunk-DC5AMYBS.js";

// node_modules/refractor/index.js
var require_refractor = __commonJS({
  "node_modules/refractor/index.js"(exports, module) {
    var refractor = require_core();
    module.exports = refractor;
    refractor.register(require_abap());
    refractor.register(require_abnf());
    refractor.register(require_actionscript());
    refractor.register(require_ada());
    refractor.register(require_agda());
    refractor.register(require_al());
    refractor.register(require_antlr4());
    refractor.register(require_apacheconf());
    refractor.register(require_apex());
    refractor.register(require_apl());
    refractor.register(require_applescript());
    refractor.register(require_aql());
    refractor.register(require_arduino());
    refractor.register(require_arff());
    refractor.register(require_asciidoc());
    refractor.register(require_asm6502());
    refractor.register(require_asmatmel());
    refractor.register(require_aspnet());
    refractor.register(require_autohotkey());
    refractor.register(require_autoit());
    refractor.register(require_avisynth());
    refractor.register(require_avro_idl());
    refractor.register(require_bash());
    refractor.register(require_basic());
    refractor.register(require_batch());
    refractor.register(require_bbcode());
    refractor.register(require_bicep());
    refractor.register(require_birb());
    refractor.register(require_bison());
    refractor.register(require_bnf());
    refractor.register(require_brainfuck());
    refractor.register(require_brightscript());
    refractor.register(require_bro());
    refractor.register(require_bsl());
    refractor.register(require_c());
    refractor.register(require_cfscript());
    refractor.register(require_chaiscript());
    refractor.register(require_cil());
    refractor.register(require_clojure());
    refractor.register(require_cmake());
    refractor.register(require_cobol());
    refractor.register(require_coffeescript());
    refractor.register(require_concurnas());
    refractor.register(require_coq());
    refractor.register(require_cpp());
    refractor.register(require_crystal());
    refractor.register(require_csharp());
    refractor.register(require_cshtml());
    refractor.register(require_csp());
    refractor.register(require_css_extras());
    refractor.register(require_csv());
    refractor.register(require_cypher());
    refractor.register(require_d());
    refractor.register(require_dart());
    refractor.register(require_dataweave());
    refractor.register(require_dax());
    refractor.register(require_dhall());
    refractor.register(require_diff());
    refractor.register(require_django());
    refractor.register(require_dns_zone_file());
    refractor.register(require_docker());
    refractor.register(require_dot());
    refractor.register(require_ebnf());
    refractor.register(require_editorconfig());
    refractor.register(require_eiffel());
    refractor.register(require_ejs());
    refractor.register(require_elixir());
    refractor.register(require_elm());
    refractor.register(require_erb());
    refractor.register(require_erlang());
    refractor.register(require_etlua());
    refractor.register(require_excel_formula());
    refractor.register(require_factor());
    refractor.register(require_false());
    refractor.register(require_firestore_security_rules());
    refractor.register(require_flow());
    refractor.register(require_fortran());
    refractor.register(require_fsharp());
    refractor.register(require_ftl());
    refractor.register(require_gap());
    refractor.register(require_gcode());
    refractor.register(require_gdscript());
    refractor.register(require_gedcom());
    refractor.register(require_gherkin());
    refractor.register(require_git());
    refractor.register(require_glsl());
    refractor.register(require_gml());
    refractor.register(require_gn());
    refractor.register(require_go_module());
    refractor.register(require_go());
    refractor.register(require_graphql());
    refractor.register(require_groovy());
    refractor.register(require_haml());
    refractor.register(require_handlebars());
    refractor.register(require_haskell());
    refractor.register(require_haxe());
    refractor.register(require_hcl());
    refractor.register(require_hlsl());
    refractor.register(require_hoon());
    refractor.register(require_hpkp());
    refractor.register(require_hsts());
    refractor.register(require_http());
    refractor.register(require_ichigojam());
    refractor.register(require_icon());
    refractor.register(require_icu_message_format());
    refractor.register(require_idris());
    refractor.register(require_iecst());
    refractor.register(require_ignore());
    refractor.register(require_inform7());
    refractor.register(require_ini());
    refractor.register(require_io());
    refractor.register(require_j());
    refractor.register(require_java());
    refractor.register(require_javadoc());
    refractor.register(require_javadoclike());
    refractor.register(require_javastacktrace());
    refractor.register(require_jexl());
    refractor.register(require_jolie());
    refractor.register(require_jq());
    refractor.register(require_js_extras());
    refractor.register(require_js_templates());
    refractor.register(require_jsdoc());
    refractor.register(require_json());
    refractor.register(require_json5());
    refractor.register(require_jsonp());
    refractor.register(require_jsstacktrace());
    refractor.register(require_jsx());
    refractor.register(require_julia());
    refractor.register(require_keepalived());
    refractor.register(require_keyman());
    refractor.register(require_kotlin());
    refractor.register(require_kumir());
    refractor.register(require_kusto());
    refractor.register(require_latex());
    refractor.register(require_latte());
    refractor.register(require_less());
    refractor.register(require_lilypond());
    refractor.register(require_liquid());
    refractor.register(require_lisp());
    refractor.register(require_livescript());
    refractor.register(require_llvm());
    refractor.register(require_log());
    refractor.register(require_lolcode());
    refractor.register(require_lua());
    refractor.register(require_magma());
    refractor.register(require_makefile());
    refractor.register(require_markdown());
    refractor.register(require_markup_templating());
    refractor.register(require_matlab());
    refractor.register(require_maxscript());
    refractor.register(require_mel());
    refractor.register(require_mermaid());
    refractor.register(require_mizar());
    refractor.register(require_mongodb());
    refractor.register(require_monkey());
    refractor.register(require_moonscript());
    refractor.register(require_n1ql());
    refractor.register(require_n4js());
    refractor.register(require_nand2tetris_hdl());
    refractor.register(require_naniscript());
    refractor.register(require_nasm());
    refractor.register(require_neon());
    refractor.register(require_nevod());
    refractor.register(require_nginx());
    refractor.register(require_nim());
    refractor.register(require_nix());
    refractor.register(require_nsis());
    refractor.register(require_objectivec());
    refractor.register(require_ocaml());
    refractor.register(require_opencl());
    refractor.register(require_openqasm());
    refractor.register(require_oz());
    refractor.register(require_parigp());
    refractor.register(require_parser());
    refractor.register(require_pascal());
    refractor.register(require_pascaligo());
    refractor.register(require_pcaxis());
    refractor.register(require_peoplecode());
    refractor.register(require_perl());
    refractor.register(require_php_extras());
    refractor.register(require_php());
    refractor.register(require_phpdoc());
    refractor.register(require_plsql());
    refractor.register(require_powerquery());
    refractor.register(require_powershell());
    refractor.register(require_processing());
    refractor.register(require_prolog());
    refractor.register(require_promql());
    refractor.register(require_properties());
    refractor.register(require_protobuf());
    refractor.register(require_psl());
    refractor.register(require_pug());
    refractor.register(require_puppet());
    refractor.register(require_pure());
    refractor.register(require_purebasic());
    refractor.register(require_purescript());
    refractor.register(require_python());
    refractor.register(require_q());
    refractor.register(require_qml());
    refractor.register(require_qore());
    refractor.register(require_qsharp());
    refractor.register(require_r());
    refractor.register(require_racket());
    refractor.register(require_reason());
    refractor.register(require_regex());
    refractor.register(require_rego());
    refractor.register(require_renpy());
    refractor.register(require_rest());
    refractor.register(require_rip());
    refractor.register(require_roboconf());
    refractor.register(require_robotframework());
    refractor.register(require_ruby());
    refractor.register(require_rust());
    refractor.register(require_sas());
    refractor.register(require_sass());
    refractor.register(require_scala());
    refractor.register(require_scheme());
    refractor.register(require_scss());
    refractor.register(require_shell_session());
    refractor.register(require_smali());
    refractor.register(require_smalltalk());
    refractor.register(require_smarty());
    refractor.register(require_sml());
    refractor.register(require_solidity());
    refractor.register(require_solution_file());
    refractor.register(require_soy());
    refractor.register(require_sparql());
    refractor.register(require_splunk_spl());
    refractor.register(require_sqf());
    refractor.register(require_sql());
    refractor.register(require_squirrel());
    refractor.register(require_stan());
    refractor.register(require_stylus());
    refractor.register(require_swift());
    refractor.register(require_systemd());
    refractor.register(require_t4_cs());
    refractor.register(require_t4_templating());
    refractor.register(require_t4_vb());
    refractor.register(require_tap());
    refractor.register(require_tcl());
    refractor.register(require_textile());
    refractor.register(require_toml());
    refractor.register(require_tremor());
    refractor.register(require_tsx());
    refractor.register(require_tt2());
    refractor.register(require_turtle());
    refractor.register(require_twig());
    refractor.register(require_typescript());
    refractor.register(require_typoscript());
    refractor.register(require_unrealscript());
    refractor.register(require_uorazor());
    refractor.register(require_uri());
    refractor.register(require_v());
    refractor.register(require_vala());
    refractor.register(require_vbnet());
    refractor.register(require_velocity());
    refractor.register(require_verilog());
    refractor.register(require_vhdl());
    refractor.register(require_vim());
    refractor.register(require_visual_basic());
    refractor.register(require_warpscript());
    refractor.register(require_wasm());
    refractor.register(require_web_idl());
    refractor.register(require_wiki());
    refractor.register(require_wolfram());
    refractor.register(require_wren());
    refractor.register(require_xeora());
    refractor.register(require_xml_doc());
    refractor.register(require_xojo());
    refractor.register(require_xquery());
    refractor.register(require_yaml());
    refractor.register(require_yang());
    refractor.register(require_zig());
  }
});

export {
  require_refractor
};
//# sourceMappingURL=chunk-DDN4MPVJ.js.map
