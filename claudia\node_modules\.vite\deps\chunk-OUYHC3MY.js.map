{"version": 3, "sources": ["../../highlight.js/lib/languages/xl.js"], "sourcesContent": ["/*\nLanguage: XL\nAuthor: <PERSON> <<EMAIL>>\nDescription: An extensible programming language, based on parse tree rewriting\nWebsite: http://xlr.sf.net\n*/\n\nfunction xl(hljs) {\n  const BUILTIN_MODULES =\n    'ObjectLoader Animate MovieCredits Slides Filters Shading Materials LensFlare Mapping VLCAudioVideo ' +\n    'StereoDecoder PointCloud NetworkAccess RemoteControl RegExp ChromaKey Snowfall NodeJS Speech Charts';\n\n  const XL_KEYWORDS = {\n    $pattern: /[a-zA-Z][a-zA-Z0-9_?]*/,\n    keyword:\n      'if then else do while until for loop import with is as where when by data constant ' +\n      'integer real text name boolean symbol infix prefix postfix block tree',\n    literal:\n      'true false nil',\n    built_in:\n      'in mod rem and or xor not abs sign floor ceil sqrt sin cos tan asin ' +\n      'acos atan exp expm1 log log2 log10 log1p pi at text_length text_range ' +\n      'text_find text_replace contains page slide basic_slide title_slide ' +\n      'title subtitle fade_in fade_out fade_at clear_color color line_color ' +\n      'line_width texture_wrap texture_transform texture scale_?x scale_?y ' +\n      'scale_?z? translate_?x translate_?y translate_?z? rotate_?x rotate_?y ' +\n      'rotate_?z? rectangle circle ellipse sphere path line_to move_to ' +\n      'quad_to curve_to theme background contents locally time mouse_?x ' +\n      'mouse_?y mouse_buttons ' +\n      BUILTIN_MODULES\n  };\n\n  const DOUBLE_QUOTE_TEXT = {\n    className: 'string',\n    begin: '\"',\n    end: '\"',\n    illegal: '\\\\n'\n  };\n  const SINGLE_QUOTE_TEXT = {\n    className: 'string',\n    begin: '\\'',\n    end: '\\'',\n    illegal: '\\\\n'\n  };\n  const LONG_TEXT = {\n    className: 'string',\n    begin: '<<',\n    end: '>>'\n  };\n  const BASED_NUMBER = {\n    className: 'number',\n    begin: '[0-9]+#[0-9A-Z_]+(\\\\.[0-9-A-Z_]+)?#?([Ee][+-]?[0-9]+)?'\n  };\n  const IMPORT = {\n    beginKeywords: 'import',\n    end: '$',\n    keywords: XL_KEYWORDS,\n    contains: [ DOUBLE_QUOTE_TEXT ]\n  };\n  const FUNCTION_DEFINITION = {\n    className: 'function',\n    begin: /[a-z][^\\n]*->/,\n    returnBegin: true,\n    end: /->/,\n    contains: [\n      hljs.inherit(hljs.TITLE_MODE, {\n        starts: {\n          endsWithParent: true,\n          keywords: XL_KEYWORDS\n        }\n      })\n    ]\n  };\n  return {\n    name: 'XL',\n    aliases: [ 'tao' ],\n    keywords: XL_KEYWORDS,\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      DOUBLE_QUOTE_TEXT,\n      SINGLE_QUOTE_TEXT,\n      LONG_TEXT,\n      FUNCTION_DEFINITION,\n      IMPORT,\n      BASED_NUMBER,\n      hljs.NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = xl;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,GAAG,MAAM;AAChB,YAAM,kBACJ;AAGF,YAAM,cAAc;AAAA,QAClB,UAAU;AAAA,QACV,SACE;AAAA,QAEF,SACE;AAAA,QACF,UACE,yjBASA;AAAA,MACJ;AAEA,YAAM,oBAAoB;AAAA,QACxB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,MACX;AACA,YAAM,oBAAoB;AAAA,QACxB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,MACX;AACA,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AACA,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,SAAS;AAAA,QACb,eAAe;AAAA,QACf,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU,CAAE,iBAAkB;AAAA,MAChC;AACA,YAAM,sBAAsB;AAAA,QAC1B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,aAAa;AAAA,QACb,KAAK;AAAA,QACL,UAAU;AAAA,UACR,KAAK,QAAQ,KAAK,YAAY;AAAA,YAC5B,QAAQ;AAAA,cACN,gBAAgB;AAAA,cAChB,UAAU;AAAA,YACZ;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,KAAM;AAAA,QACjB,UAAU;AAAA,QACV,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}