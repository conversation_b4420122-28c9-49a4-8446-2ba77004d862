{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2040997289075261528, "path": 9231407544665766761, "deps": [[561782849581144631, "html5ever", false, 2110388355923296001], [1200537532907108615, "url<PERSON><PERSON>n", false, 10618455366097760799], [3129130049864710036, "memchr", false, 13714291482932232515], [3150220818285335163, "url", false, 14171641885309381843], [3191507132440681679, "serde_untagged", false, 8994752780394247461], [4899080583175475170, "semver", false, 15124389970013230145], [5986029879202738730, "log", false, 16506617143423921061], [6213549728662707793, "serde_with", false, 7974355569650706209], [6262254372177975231, "kuchiki", false, 9685128963661526211], [6606131838865521726, "ctor", false, 941412944321132582], [7170110829644101142, "json_patch", false, 17937471332595491588], [8319709847752024821, "uuid", false, 7751201265180955214], [9010263965687315507, "http", false, 12213520909095809930], [9451456094439810778, "regex", false, 7725510663390383755], [9689903380558560274, "serde", false, 7541355907771353291], [10806645703491011684, "thiserror", false, 5059168433112344807], [11989259058781683633, "dunce", false, 18275498118312540539], [13625485746686963219, "anyhow", false, 12315510034218800024], [14132538657330703225, "brotli", false, 15464078312287058537], [15367738274754116744, "serde_json", false, 17470562606684216992], [15609422047640926750, "toml", false, 993476378258011714], [15622660310229662834, "walkdir", false, 1861360244275281461], [17146114186171651583, "infer", false, 9499547596572947638], [17155886227862585100, "glob", false, 2983592991386580428], [17186037756130803222, "phf", false, 976242675053152361]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-ecca806569ff03b5\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}